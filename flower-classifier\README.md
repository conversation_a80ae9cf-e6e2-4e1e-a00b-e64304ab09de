# 🌸 Công cụ Phân loại Hoa - Flower Classification Tool

Ứng dụng web sử dụng Google Gemini AI để tự động phân loại hình ảnh hoa theo các danh mục khác nhau như bó hoa, giỏ hoa, bình hoa, v.v.

## ✨ Tính năng chính

- **Phân loại tự động**: Sử dụng Google Gemini AI để nhận diện và phân loại hình ảnh hoa
- **Xử lý hàng loạt**: Upload và xử lý nhiều hình ảnh cùng lúc
- **Giao diện thân thiện**: Drag & drop, progress tracking, và hiển thị kết quả trực quan
- **Tổ chức file tự động**: Tự động sắp xếp hình ảnh theo danh mục đã phân loại
- **Export dữ liệu**: T<PERSON>i xuống file theo danh mục hoặc export thống kê

## 🎯 <PERSON><PERSON> mục phân loại

- **Bó hoa**: <PERSON><PERSON> đượ<PERSON> buộc thành bó, thường có giấy gói hoặc ruy băng
- **Giỏ hoa**: Hoa được cắm trong giỏ hoặc thúng
- **Bình hoa**: Hoa được cắm trong bình, lọ, hoặc chậu
- **Khác**: Các cách sắp xếp khác như vòng hoa, hoa cài áo, etc.

## 🚀 Cài đặt và Chạy

### Yêu cầu hệ thống
- Node.js 16+ 
- npm hoặc yarn
- Trình duyệt hiện đại (Chrome, Firefox, Safari, Edge)

### Bước 1: Clone repository
```bash
git clone <repository-url>
cd flower-classifier
```

### Bước 2: Cài đặt dependencies
```bash
npm install
```

### Bước 3: Cấu hình API Key
Mở file `src/services/geminiService.js` và thay thế API key:
```javascript
const API_KEY = 'YOUR_GEMINI_API_KEY_HERE';
```

**Lưu ý**: Trong môi trường production, nên sử dụng environment variables để bảo mật API key.

### Bước 4: Chạy ứng dụng
```bash
npm run dev
```

Ứng dụng sẽ chạy tại: `http://localhost:5173/`

## 📖 Hướng dẫn sử dụng

### 1. Upload hình ảnh
- Kéo thả file vào vùng upload hoặc click để chọn file
- Hỗ trợ định dạng: JPG, PNG, GIF, WebP (tối đa 10MB/file)
- Có thể upload nhiều file cùng lúc

### 2. Xử lý phân loại
- Click "Bắt đầu phân loại" để bắt đầu
- Theo dõi tiến độ xử lý real-time
- Xem kết quả chi tiết cho từng hình ảnh

### 3. Quản lý kết quả
- Xem kết quả theo danh mục
- Tải xuống file theo danh mục (ZIP)
- Lưu trực tiếp vào thư mục (nếu browser hỗ trợ)
- Export thống kê dưới dạng CSV

## 🛠️ Cấu trúc dự án

```
flower-classifier/
├── src/
│   ├── components/          # React components
│   │   ├── ImageUploader.jsx
│   │   ├── ProcessingProgress.jsx
│   │   └── ResultsDisplay.jsx
│   ├── services/           # Business logic
│   │   ├── geminiService.js
│   │   └── fileService.js
│   ├── App.jsx            # Main app component
│   ├── main.js           # Entry point
│   └── style.css         # Global styles
├── public/               # Static assets
├── package.json         # Dependencies
└── README.md           # Documentation
```

## 🔧 Cấu hình nâng cao

### Environment Variables
Tạo file `.env` trong thư mục gốc:
```env
VITE_GEMINI_API_KEY=your_api_key_here
```

Sau đó cập nhật `geminiService.js`:
```javascript
const API_KEY = import.meta.env.VITE_GEMINI_API_KEY;
```

### Tùy chỉnh danh mục
Chỉnh sửa prompt trong `geminiService.js` để thêm/sửa danh mục phân loại.

## 🌐 Triển khai (Deployment)

### Build cho production
```bash
npm run build
```

### Deploy lên Vercel
```bash
npm install -g vercel
vercel
```

### Deploy lên Netlify
```bash
npm run build
# Upload thư mục dist/ lên Netlify
```

## 🔍 Troubleshooting

### Lỗi API Key
- Kiểm tra API key có đúng không
- Đảm bảo API key có quyền truy cập Gemini API
- Kiểm tra quota và billing của Google Cloud

### Lỗi CORS
- Gemini API hỗ trợ CORS, không cần proxy
- Nếu gặp lỗi, kiểm tra network và firewall

### Lỗi Upload File
- Kiểm tra định dạng file (chỉ hỗ trợ hình ảnh)
- Kiểm tra kích thước file (tối đa 10MB)
- Đảm bảo trình duyệt hỗ trợ File API

## 📊 Hiệu suất

- **Thời gian xử lý**: ~2-5 giây/hình ảnh
- **Độ chính xác**: ~85-95% (tùy thuộc vào chất lượng hình ảnh)
- **Giới hạn**: 60 requests/phút (theo quota Gemini API)

## 🤝 Đóng góp

1. Fork repository
2. Tạo feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to branch (`git push origin feature/AmazingFeature`)
5. Mở Pull Request

## 📄 License

Distributed under the MIT License. See `LICENSE` for more information.

## 📞 Hỗ trợ

Nếu gặp vấn đề, vui lòng:
1. Kiểm tra phần Troubleshooting
2. Tạo issue trên GitHub
3. Liên hệ qua email: [<EMAIL>]

## 🙏 Acknowledgments

- [Google Gemini AI](https://ai.google.dev/) - AI model cho phân loại hình ảnh
- [React](https://reactjs.org/) - UI framework
- [Vite](https://vitejs.dev/) - Build tool
- [Tailwind CSS](https://tailwindcss.com/) - CSS framework
- [Lucide React](https://lucide.dev/) - Icon library
