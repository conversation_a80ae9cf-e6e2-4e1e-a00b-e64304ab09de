import React, { useState } from 'react';
import { Flower2, Settings, Info, AlertCircle, HardDrive, Zap } from 'lucide-react';
import ImageUploader from './components/ImageUploader';
import ProcessingProgress from './components/ProcessingProgress';
import ResultsDisplay from './components/ResultsDisplay';
import ErrorBoundary from './components/ErrorBoundary';
import AutoSaveManager from './components/AutoSaveManager';
import RateLimitManager from './components/RateLimitManager';
import { FullScreenLoading } from './components/LoadingSpinner';
import { useAppState } from './hooks/useAppState';
import './App.css';

function App() {
  const [showInfo, setShowInfo] = useState(false);
  const [showAutoSaveManager, setShowAutoSaveManager] = useState(false);
  const [showRateLimitManager, setShowRateLimitManager] = useState(false);

  const {
    isProcessing,
    progress,
    results,
    error,
    isInitialized,
    handleFilesSelected,
    handleClearResults,
    handleRetry,
    exportResults,
    statistics,
    hasResults,
    hasError,
    canProcess
  } = useAppState();

  // Show loading screen while initializing
  if (!isInitialized) {
    return <FullScreenLoading message="Đang khởi động ứng dụng..." />;
  }

  return (
    <ErrorBoundary>
      <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-3">
              <Flower2 className="h-8 w-8 text-pink-500" />
              <div>
                <h1 className="text-xl font-bold text-gray-900">
                  Công cụ phân loại hoa
                </h1>
                <p className="text-sm text-gray-500">
                  Sử dụng AI để tự động phân loại hình ảnh hoa
                </p>
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              {/* Statistics */}
              {statistics && (
                <div className="hidden md:flex items-center space-x-4 text-sm text-gray-600">
                  <div className="flex items-center space-x-1">
                    <span className="font-medium">{statistics.total}</span>
                    <span>hình ảnh</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <span className="font-medium text-green-600">{statistics.successful}</span>
                    <span>thành công</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <span className="font-medium">{statistics.successRate}%</span>
                    <span>độ chính xác</span>
                  </div>
                </div>
              )}

              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setShowRateLimitManager(true)}
                  className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
                  title="Quản lý Rate Limit"
                >
                  <Zap className="h-5 w-5" />
                </button>
                <button
                  onClick={() => setShowAutoSaveManager(true)}
                  className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
                  title="Quản lý Auto-Save"
                >
                  <HardDrive className="h-5 w-5" />
                </button>
                <button
                  onClick={() => setShowInfo(!showInfo)}
                  className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
                  title="Thông tin hướng dẫn"
                >
                  <Info className="h-5 w-5" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Error notification */}
      {hasError && (
        <div className="bg-red-50 border-b border-red-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div className="flex items-center justify-between bg-red-100 rounded-lg p-4">
              <div className="flex items-center">
                <AlertCircle className="h-5 w-5 text-red-500 mr-3" />
                <div>
                  <h3 className="text-sm font-medium text-red-800">Có lỗi xảy ra</h3>
                  <p className="text-sm text-red-700 mt-1">{error}</p>
                </div>
              </div>
              <button
                onClick={handleRetry}
                className="text-sm text-red-600 hover:text-red-800 font-medium"
              >
                Thử lại
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Thông tin hướng dẫn */}
      {showInfo && (
        <div className="bg-blue-50 border-b border-blue-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div className="bg-white rounded-lg p-6 shadow-sm">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Hướng dẫn sử dụng
              </h3>
              <div className="grid md:grid-cols-2 gap-6 text-sm text-gray-600">
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Các bước thực hiện:</h4>
                  <ol className="list-decimal list-inside space-y-1">
                    <li>Kéo thả hoặc chọn hình ảnh hoa cần phân loại</li>
                    <li>Nhấn "Bắt đầu phân loại" để xử lý</li>
                    <li>Xem kết quả và tải xuống theo danh mục</li>
                  </ol>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Danh mục phân loại:</h4>
                  <ul className="list-disc list-inside space-y-1">
                    <li><strong>Bó hoa:</strong> Hoa được buộc thành bó</li>
                    <li><strong>Giỏ hoa:</strong> Hoa được cắm trong giỏ</li>
                    <li><strong>Bình hoa:</strong> Hoa được cắm trong bình/lọ</li>
                    <li><strong>Khác:</strong> Các cách sắp xếp khác</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Main content */}
      <main className="max-w-7xl mx-auto py-8">
        {/* Upload section */}
        <ImageUploader
          onFilesSelected={handleFilesSelected}
          isProcessing={isProcessing || hasError}
          disabled={!canProcess}
        />
        
        {/* Progress section */}
        <ProcessingProgress 
          progress={progress}
          results={results}
        />
        
        {/* Results section */}
        <ResultsDisplay 
          results={results}
          onClear={handleClearResults}
        />
      </main>

      {/* Footer */}
      <footer className="bg-white border-t mt-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="text-center text-sm text-gray-500">
            <p>Công cụ phân loại hoa sử dụng Google Gemini AI</p>
            <p className="mt-1">Hỗ trợ định dạng: JPG, PNG, GIF, WebP (tối đa 10MB)</p>
          </div>
        </div>
      </footer>
      </div>

      {/* Auto-Save Manager Modal */}
      <AutoSaveManager
        isVisible={showAutoSaveManager}
        onClose={() => setShowAutoSaveManager(false)}
        results={results}
      />

      {/* Rate Limit Manager Modal */}
      <RateLimitManager
        isVisible={showRateLimitManager}
        onClose={() => setShowRateLimitManager(false)}
        onSettingsChange={(settings) => {
          console.log('Rate limit settings updated:', settings);
        }}
      />
    </ErrorBoundary>
  );
}

export default App;
