import { GoogleGenerativeAI } from '@google/generative-ai';

// API Key - trong thực tế nên lưu trong environment variables
const API_KEY = 'AIzaSyAVq6hfli-gbIHrHxBmaInNiTLThPVC4o4';

class GeminiService {
  constructor() {
    this.genAI = new GoogleGenerativeAI(API_KEY);
    // Sử dụng model mạnh nhất cho độ chính xác cao
    this.model = this.genAI.getGenerativeModel({ 
      model: 'gemini-1.5-pro',
      generationConfig: {
        temperature: 0.1, // Giảm randomness để tăng consistency
        topK: 1,
        topP: 0.8,
        maxOutputTokens: 2048,
      },
      safetySettings: [
        {
          category: "HARM_CATEGORY_HARASSMENT",
          threshold: "BLOCK_MEDIUM_AND_ABOVE",
        },
        {
          category: "HARM_CATEGORY_HATE_SPEECH", 
          threshold: "BLOCK_MEDIUM_AND_ABOVE",
        },
        {
          category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
          threshold: "BLOCK_MEDIUM_AND_ABOVE",
        },
        {
          category: "HARM_CATEGORY_DANGEROUS_CONTENT",
          threshold: "BLOCK_MEDIUM_AND_ABOVE",
        },
      ],
    });
    
    // Cache cho kết quả đã xử lý
    this.cache = new Map();
    this.processingQueue = [];
    this.isProcessing = false;
    
    // Rate limiting management
    this.requestCount = 0;
    this.requestWindow = 60000; // 1 minute
    this.maxRequestsPerMinute = 12; // Conservative limit
    this.lastRequestTime = 0;
    this.rateLimitResetTime = 0;
    
    // Queue management
    this.pendingRequests = [];
    this.isRateLimited = false;
    this.retryDelays = [2000, 5000, 10000, 30000, 60000]; // Progressive delays
  }

  /**
   * Kiểm tra xem API key đã được cấu hình chưa
   * @returns {boolean} - True nếu API key đã được cấu hình
   */
  isConfigured() {
    return API_KEY && API_KEY !== 'YOUR_GEMINI_API_KEY_HERE' && API_KEY.length > 10;
  }

  /**
   * Kiểm tra rate limit
   */
  checkRateLimit() {
    const now = Date.now();
    
    // Reset counter nếu đã qua window
    if (now - this.lastRequestTime > this.requestWindow) {
      this.requestCount = 0;
      this.lastRequestTime = now;
    }
    
    // Kiểm tra có vượt limit không
    if (this.requestCount >= this.maxRequestsPerMinute) {
      this.isRateLimited = true;
      this.rateLimitResetTime = this.lastRequestTime + this.requestWindow;
      return false;
    }
    
    return true;
  }

  /**
   * Đợi cho đến khi rate limit reset
   */
  async waitForRateLimit() {
    if (!this.isRateLimited) return;
    
    const now = Date.now();
    const waitTime = Math.max(0, this.rateLimitResetTime - now);
    
    if (waitTime > 0) {
      console.log(`Rate limited. Waiting ${Math.ceil(waitTime / 1000)} seconds...`);
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }
    
    this.isRateLimited = false;
    this.requestCount = 0;
  }

  /**
   * Lấy delay cho lần retry tiếp theo
   */
  getNextRetryDelay() {
    const baseDelay = 3000; // 3 seconds
    const maxDelay = 300000; // 5 minutes
    const jitter = Math.random() * 1000; // Random jitter
    
    return Math.min(maxDelay, baseDelay * Math.pow(2, this.requestCount % 5) + jitter);
  }

  /**
   * Kiểm tra trạng thái rate limit
   */
  getRateLimitStatus() {
    return {
      isRateLimited: this.isRateLimited,
      requestCount: this.requestCount,
      maxRequests: this.maxRequestsPerMinute,
      resetTime: this.rateLimitResetTime,
      timeUntilReset: Math.max(0, this.rateLimitResetTime - Date.now())
    };
  }

  /**
   * Thực hiện request với rate limiting
   */
  async makeRateLimitedRequest(requestFn, maxRetries = 3) {
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      // Kiểm tra rate limit
      if (!this.checkRateLimit()) {
        await this.waitForRateLimit();
      }
      
      try {
        // Increment counter
        this.requestCount++;
        this.lastRequestTime = Date.now();
        
        // Thực hiện request
        const result = await requestFn();
        return result;
        
      } catch (error) {
        console.error(`Request attempt ${attempt + 1} failed:`, error);
        
        // Kiểm tra nếu là rate limit error
        if (error.message.includes('quota') || 
            error.message.includes('rate') || 
            error.message.includes('limit') ||
            error.status === 429) {
          
          this.isRateLimited = true;
          const delay = this.retryDelays[Math.min(attempt, this.retryDelays.length - 1)];
          
          console.log(`Rate limited. Waiting ${delay / 1000} seconds before retry...`);
          await new Promise(resolve => setTimeout(resolve, delay));
          continue;
        }
        
        // Nếu không phải rate limit error và đã hết retry
        if (attempt === maxRetries) {
          throw error;
        }
        
        // Exponential backoff cho các lỗi khác
        const delay = Math.min(30000, Math.pow(2, attempt) * 1000);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  /**
   * Chuyển đổi file thành format cho Gemini API
   * @param {File} file - File cần chuyển đổi
   * @returns {Promise<Object>} - Object chứa data và mimeType
   */
  async fileToGenerativePart(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const base64Data = reader.result.split(',')[1];
        resolve({
          inlineData: {
            data: base64Data,
            mimeType: file.type
          }
        });
      };
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  }

  /**
   * Kiểm tra xem file có phải là hình ảnh hợp lệ không
   * @param {File} file - File cần kiểm tra
   * @returns {boolean} - True nếu là hình ảnh hợp lệ
   */
  isValidImageFile(file) {
    const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    const maxSize = 10 * 1024 * 1024; // 10MB
    
    return validTypes.includes(file.type) && file.size <= maxSize;
  }

  /**
   * Tạo hash cho file để cache
   */
  async getFileHash(file) {
    const arrayBuffer = await file.arrayBuffer();
    const hashBuffer = await crypto.subtle.digest('SHA-256', arrayBuffer);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  }

  /**
   * Phân loại hình ảnh hoa với AI nâng cao và rate limiting
   * @param {File} imageFile - File hình ảnh cần phân loại
   * @returns {Promise<Object>} - Kết quả phân loại
   */
  async classifyFlowerImage(imageFile) {
    const startTime = Date.now();
    
    try {
      // Kiểm tra cache
      const fileHash = await this.getFileHash(imageFile);
      if (this.cache.has(fileHash)) {
        return this.cache.get(fileHash);
      }

      // Chuyển đổi file thành base64
      const imageData = await this.fileToGenerativePart(imageFile);
      
      // Advanced prompt engineering cho độ chính xác cao
      const prompt = `
Bạn là chuyên gia phân loại hoa với 20 năm kinh nghiệm. Hãy phân tích hình ảnh này một cách chi tiết và chính xác.

NHIỆM VỤ: Xác định loại cách sắp xếp/trình bày hoa trong hình ảnh.

PHÂN TÍCH THEO CÁC BƯỚC:
1. Quan sát container/vật chứa hoa
2. Xác định cách sắp xếp và trình bày
3. Đánh giá độ tin cậy dựa trên các đặc điểm rõ ràng

DANH MỤC PHÂN LOẠI (chọn 1):
- "Bó hoa": Hoa được buộc/cột thành bó, có thể có giấy gói, ruy băng, hoặc dây buộc. Thường cầm tay được.
- "Giỏ hoa": Hoa được cắm/sắp xếp trong giỏ, rổ, thúng (làm từ mây, tre, nhựa, kim loại)
- "Bình hoa": Hoa được cắm trong bình, lọ, chậu, ly, hoặc bất kỳ vật chứa nước nào
- "Lẵng hoa": Hoa được sắp xếp trong lẵng (thường lớn, dùng cho khai trương, tang lễ)
- "Vòng hoa": Hoa được làm thành vòng tròn (vòng đeo cổ, vòng trang trí)
- "Hoa cài": Hoa nhỏ để cài áo, tóc, hoặc trang trí cá nhân
- "Khác": Các cách sắp xếp không thuộc các loại trên

ĐỊNH DẠNG TRẢ VỀ (JSON thuần, không markdown):
{
  "category": "tên danh mục chính xác",
  "confidence": số từ 0-100,
  "description": "mô tả chi tiết cách sắp xếp và container",
  "flowers": ["danh sách loại hoa nhận diện được"],
  "colors": ["màu sắc chủ đạo"],
  "container": "mô tả vật chứa/cách trình bày",
  "style": "phong cách sắp xếp",
  "reasoning": "lý do phân loại vào danh mục này"
}

Hãy phân tích cẩn thận và trả về JSON chính xác.
      `;

      // Sử dụng rate-limited request
      const result = await this.makeRateLimitedRequest(async () => {
        return await this.model.generateContent([prompt, imageData]);
      });
      
      const response = await result.response;
      const text = response.text();
      
      // Advanced JSON parsing với fallback
      let classification;
      try {
        // Thử parse trực tiếp
        classification = JSON.parse(text);
      } catch (e1) {
        try {
          // Tìm JSON trong text
          const jsonMatch = text.match(/\{[\s\S]*\}/);
          if (jsonMatch) {
            classification = JSON.parse(jsonMatch[0]);
          } else {
            throw new Error('No JSON found');
          }
        } catch (e2) {
          // Fallback: tạo kết quả từ text analysis
          classification = this.parseTextToClassification(text, imageFile.name);
        }
      }
      
      // Validate và enhance kết quả
      const validatedResult = this.validateAndEnhanceResult(classification);
      
      // Cache kết quả
      const finalResult = {
        success: true,
        data: validatedResult,
        processingTime: Date.now() - startTime,
        modelUsed: 'gemini-1.5-pro'
      };
      
      this.cache.set(fileHash, finalResult);
      return finalResult;
      
    } catch (error) {
      console.error('Lỗi khi gọi Gemini API:', error);
      
      // Enhanced error handling cho rate limiting
      if (error.message.includes('quota') || 
          error.message.includes('rate') || 
          error.message.includes('limit') ||
          error.status === 429) {
        
        this.isRateLimited = true;
        const waitTime = this.getNextRetryDelay();
        
        return {
          success: false,
          error: `Đã vượt quá giới hạn API. Hệ thống sẽ tự động thử lại sau ${Math.ceil(waitTime / 1000)} giây.`,
          retryAfter: waitTime,
          isRateLimited: true,
          canRetry: true
        };
      }
      
      return {
        success: false,
        error: error.message || 'Lỗi không xác định khi phân loại hình ảnh',
        processingTime: Date.now() - startTime
      };
    }
  }

  /**
   * Parse text response thành classification khi JSON parsing thất bại
   */
  parseTextToClassification(text, fileName) {
    const categories = ['Bó hoa', 'Giỏ hoa', 'Bình hoa', 'Lẵng hoa', 'Vòng hoa', 'Hoa cài', 'Khác'];

    // Tìm category trong text
    let category = 'Khác';
    let confidence = 50;

    for (const cat of categories) {
      if (text.toLowerCase().includes(cat.toLowerCase())) {
        category = cat;
        confidence = 75;
        break;
      }
    }

    return {
      category,
      confidence,
      description: `Phân loại dựa trên text analysis: ${text.substring(0, 100)}...`,
      flowers: this.extractFlowersFromText(text),
      colors: this.extractColorsFromText(text),
      container: 'Không xác định',
      style: 'Không xác định',
      reasoning: 'Fallback classification từ text analysis'
    };
  }

  /**
   * Validate và enhance kết quả classification
   */
  validateAndEnhanceResult(classification) {
    const validCategories = ['Bó hoa', 'Giỏ hoa', 'Bình hoa', 'Lẵng hoa', 'Vòng hoa', 'Hoa cài', 'Khác'];

    // Validate category
    if (!validCategories.includes(classification.category)) {
      classification.category = 'Khác';
      classification.confidence = Math.max(0, (classification.confidence || 50) - 20);
    }

    // Validate confidence
    classification.confidence = Math.max(0, Math.min(100, classification.confidence || 50));

    // Ensure required fields
    classification.description = classification.description || 'Không có mô tả';
    classification.flowers = Array.isArray(classification.flowers) ? classification.flowers : [];
    classification.colors = Array.isArray(classification.colors) ? classification.colors : [];
    classification.container = classification.container || 'Không xác định';
    classification.style = classification.style || 'Không xác định';
    classification.reasoning = classification.reasoning || 'Phân loại tự động';

    return classification;
  }

  /**
   * Extract flower names từ text
   */
  extractFlowersFromText(text) {
    const commonFlowers = [
      'hoa hồng', 'hoa cúc', 'hoa ly', 'hoa tulip', 'hoa lan', 'hoa hướng dương',
      'hoa cẩm chướng', 'hoa baby', 'hoa đồng tiền', 'hoa cúc họa mi'
    ];

    const found = [];
    const lowerText = text.toLowerCase();

    for (const flower of commonFlowers) {
      if (lowerText.includes(flower)) {
        found.push(flower.charAt(0).toUpperCase() + flower.slice(1));
      }
    }

    return found;
  }

  /**
   * Extract colors từ text
   */
  extractColorsFromText(text) {
    const commonColors = [
      'đỏ', 'xanh', 'vàng', 'trắng', 'hồng', 'tím', 'cam', 'xanh lá'
    ];

    const found = [];
    const lowerText = text.toLowerCase();

    for (const color of commonColors) {
      if (lowerText.includes(color)) {
        found.push(color.charAt(0).toUpperCase() + color.slice(1));
      }
    }

    return found;
  }

  /**
   * Sequential processing để tránh rate limit
   * @param {File[]} files - Danh sách files cần xử lý
   * @param {Function} onProgress - Callback để báo cáo tiến độ
   * @param {Object} options - Tùy chọn xử lý
   * @returns {Promise<Array>} - Danh sách kết quả
   */
  async classifyMultipleImages(files, onProgress = null, options = {}) {
    const {
      retryAttempts = 3,
      respectRateLimit = true
    } = options;

    const validFiles = files.filter(file => this.isValidImageFile(file));
    const results = [];
    const startTime = Date.now();

    for (let i = 0; i < validFiles.length; i++) {
      const file = validFiles[i];
      const fileStartTime = Date.now();

      if (onProgress) {
        onProgress({
          current: i + 1,
          total: validFiles.length,
          fileName: file.name,
          status: 'processing',
          rateLimitStatus: this.getRateLimitStatus()
        });
      }

      let result;
      let attempts = 0;

      // Enhanced retry logic với rate limit handling
      while (attempts <= retryAttempts) {
        try {
          // Kiểm tra rate limit trước khi xử lý
          if (respectRateLimit && this.isRateLimited) {
            await this.waitForRateLimit();
          }

          result = await this.classifyFlowerImage(file);

          // Nếu thành công, break khỏi retry loop
          if (result.success) {
            break;
          }

          // Nếu là rate limit error, đợi và retry
          if (result.isRateLimited) {
            await new Promise(resolve => setTimeout(resolve, result.retryAfter));
            attempts++;
            continue;
          }

          // Các lỗi khác
          break;

        } catch (error) {
          attempts++;
          if (attempts > retryAttempts) {
            result = {
              success: false,
              error: error.message,
              attempts: attempts
            };
            break;
          } else {
            // Progressive delay
            const delay = this.getNextRetryDelay();
            await new Promise(resolve => setTimeout(resolve, delay));
          }
        }
      }

      const processingTime = Date.now() - fileStartTime;

      if (onProgress) {
        onProgress({
          current: i + 1,
          total: validFiles.length,
          fileName: file.name,
          status: result.success ? 'completed' : 'error',
          processingTime,
          rateLimitStatus: this.getRateLimitStatus(),
          attempts
        });
      }

      results.push({
        file: file,
        fileName: file.name,
        result: result,
        processingTime,
        attempts
      });

      // Intelligent delay giữa các request
      if (i < validFiles.length - 1) {
        let delay = 5000; // Base delay 5 seconds để tránh rate limit

        // Tăng delay nếu gặp rate limit
        if (result.isRateLimited) {
          delay = Math.max(delay, result.retryAfter || 10000);
        }

        // Random jitter để tránh thundering herd
        delay += Math.random() * 2000;

        if (onProgress) {
          onProgress({
            current: i + 1,
            total: validFiles.length,
            status: 'waiting',
            waitTime: delay,
            message: `Đang đợi ${Math.ceil(delay / 1000)} giây để tránh rate limit...`
          });
        }

        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    // Tính toán thống kê cuối
    const totalTime = Date.now() - startTime;
    const successCount = results.filter(r => r.result.success).length;

    if (onProgress) {
      onProgress({
        current: validFiles.length,
        total: validFiles.length,
        status: 'completed',
        summary: {
          totalFiles: validFiles.length,
          successful: successCount,
          failed: validFiles.length - successCount,
          totalTime,
          avgTimePerFile: totalTime / validFiles.length,
          successRate: (successCount / validFiles.length) * 100
        }
      });
    }

    return results;
  }

  /**
   * Clear cache để giải phóng memory
   */
  clearCache() {
    this.cache.clear();
  }

  /**
   * Lấy thống kê cache
   */
  getCacheStats() {
    return {
      size: this.cache.size,
      memoryUsage: JSON.stringify([...this.cache.values()]).length
    };
  }
}

export default new GeminiService();

    // Sử dụng model nhanh nhất với cấu hình tối ưu
    this.model = this.genAI.getGenerativeModel({
      model: 'gemini-1.5-flash', // Flash model nhanh hơn Pro
      generationConfig: {
        temperature: 0.1, // Giảm randomness để tăng consistency
        topK: 1,
        topP: 0.8,
        maxOutputTokens: 1024, // Giảm token để tăng tốc
        candidateCount: 1,
      },
      safetySettings: [
        {
          category: "HARM_CATEGORY_HARASSMENT",
          threshold: "BLOCK_MEDIUM_AND_ABOVE",
        },
        {
          category: "HARM_CATEGORY_HATE_SPEECH",
          threshold: "BLOCK_MEDIUM_AND_ABOVE",
        },
        {
          category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
          threshold: "BLOCK_MEDIUM_AND_ABOVE",
        },
        {
          category: "HARM_CATEGORY_DANGEROUS_CONTENT",
          threshold: "BLOCK_MEDIUM_AND_ABOVE",
        },
      ],
    });

    // Enhanced caching system
    this.cache = new Map();
    this.imageHashCache = new Map();
    this.resultCache = new Map();

    // Rate limiting với cấu hình tối ưu
    this.requestCount = 0;
    this.requestWindow = 60000; // 1 minute
    this.maxRequestsPerMinute = 20; // Tăng lên cho Flash model
    this.lastRequestTime = 0;
    this.rateLimitResetTime = 0;
    this.isRateLimited = false;

    // Performance tracking
    this.performanceStats = {
      totalRequests: 0,
      successfulRequests: 0,
      averageResponseTime: 0,
      cacheHits: 0,
      cacheMisses: 0
    };

    // Parallel processing queue
    this.processingQueue = [];
    this.maxConcurrent = 3; // Xử lý song song 3 request
    this.activeRequests = 0;
  }

  /**
   * Kiểm tra xem API key đã được cấu hình chưa
   */
  isConfigured() {
    return API_KEY && API_KEY !== 'YOUR_GEMINI_API_KEY_HERE' && API_KEY.length > 10;
  }

  /**
   * Tạo hash nhanh cho file sử dụng Web Crypto API
   */
  async getFileHashFast(file) {
    const cacheKey = `${file.name}_${file.size}_${file.lastModified}`;
    if (this.imageHashCache.has(cacheKey)) {
      return this.imageHashCache.get(cacheKey);
    }

    // Sử dụng metadata thay vì hash toàn bộ file để tăng tốc
    const quickHash = `${file.name}_${file.size}_${file.lastModified}_${file.type}`;
    this.imageHashCache.set(cacheKey, quickHash);
    return quickHash;
  }

  /**
   * Tiền xử lý hình ảnh tối ưu cho tốc độ
   */
  async preprocessImageFast(file) {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        // Resize tối ưu: giảm kích thước để tăng tốc mà vẫn giữ chất lượng
        const maxSize = 800; // Giảm từ 1024 xuống 800
        let { width, height } = img;

        if (width > height && width > maxSize) {
          height = (height * maxSize) / width;
          width = maxSize;
        } else if (height > maxSize) {
          width = (width * maxSize) / height;
          height = maxSize;
        }

        canvas.width = width;
        canvas.height = height;

        // Tối ưu rendering
        ctx.imageSmoothingEnabled = true;
        ctx.imageSmoothingQuality = 'medium'; // Thay vì high để tăng tốc
        ctx.drawImage(img, 0, 0, width, height);

        canvas.toBlob(resolve, 'image/jpeg', 0.85); // Giảm quality để tăng tốc
      };

      img.src = URL.createObjectURL(file);
    });
  }

  /**
   * Chuyển đổi file thành format cho Gemini API với tối ưu
   */
  async fileToGenerativePart(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const base64Data = reader.result.split(',')[1];
        resolve({
          inlineData: {
            data: base64Data,
            mimeType: file.type
          }
        });
      };
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  }

  /**
   * Kiểm tra file hợp lệ
   */
  isValidImageFile(file) {
    const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    const maxSize = 10 * 1024 * 1024; // 10MB
    return validTypes.includes(file.type) && file.size <= maxSize;
  }

  /**
   * Rate limiting thông minh
   */
  checkRateLimit() {
    const now = Date.now();

    if (now - this.lastRequestTime > this.requestWindow) {
      this.requestCount = 0;
      this.lastRequestTime = now;
    }

    if (this.requestCount >= this.maxRequestsPerMinute) {
      this.isRateLimited = true;
      this.rateLimitResetTime = this.lastRequestTime + this.requestWindow;
      return false;
    }

    return true;
  }

  /**
   * Đợi rate limit reset
   */
  async waitForRateLimit() {
    if (!this.isRateLimited) return;

    const now = Date.now();
    const waitTime = Math.max(0, this.rateLimitResetTime - now);

    if (waitTime > 0) {
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }

    this.isRateLimited = false;
    this.requestCount = 0;
  }

  /**
   * Lấy trạng thái rate limit
   */
  getRateLimitStatus() {
    return {
      isRateLimited: this.isRateLimited,
      requestCount: this.requestCount,
      maxRequests: this.maxRequestsPerMinute,
      resetTime: this.rateLimitResetTime,
      timeUntilReset: Math.max(0, this.rateLimitResetTime - Date.now()),
      activeRequests: this.activeRequests
    };
  }

  /**
   * Prompt tối ưu cho tốc độ và độ chính xác
   */
  getOptimizedPrompt() {
    return `Phân tích hình ảnh hoa này và trả về JSON:

PHÂN LOẠI:
- "Bó hoa": Hoa buộc thành bó, có giấy gói/ruy băng
- "Giỏ hoa": Hoa trong giỏ/rổ/thúng
- "Bình hoa": Hoa trong bình/lọ/chậu
- "Lẵng hoa": Lẵng hoa lớn (khai trương/tang lễ)
- "Vòng hoa": Hoa làm thành vòng tròn
- "Hoa cài": Hoa nhỏ cài áo/tóc
- "Khác": Không thuộc loại trên

JSON (chỉ trả JSON, không text khác):
{
  "category": "tên danh mục",
  "confidence": số 0-100,
  "description": "mô tả ngắn gọn",
  "flowers": ["loại hoa"],
  "colors": ["màu chủ đạo"],
  "reasoning": "lý do phân loại"
}`;
  }

  /**
   * Phân loại hình ảnh với tối ưu tốc độ tối đa
   */
  async classifyFlowerImage(imageFile) {
    const startTime = Date.now();
    this.performanceStats.totalRequests++;

    try {
      // Kiểm tra cache nhanh
      const fileHash = await this.getFileHashFast(imageFile);
      if (this.resultCache.has(fileHash)) {
        this.performanceStats.cacheHits++;
        const cachedResult = this.resultCache.get(fileHash);
        return {
          ...cachedResult,
          fromCache: true,
          processingTime: Date.now() - startTime
        };
      }

      this.performanceStats.cacheMisses++;

      // Tiền xử lý nhanh
      const processedFile = await this.preprocessImageFast(imageFile);
      const imageData = await this.fileToGenerativePart(processedFile);

      // Kiểm tra rate limit
      if (!this.checkRateLimit()) {
        await this.waitForRateLimit();
      }

      // Increment counters
      this.requestCount++;
      this.activeRequests++;
      this.lastRequestTime = Date.now();

      // Gọi API với prompt tối ưu
      const result = await this.model.generateContent([
        this.getOptimizedPrompt(),
        imageData
      ]);

      this.activeRequests--;

      const response = await result.response;
      const text = response.text();

      // Parse JSON nhanh
      let classification;
      try {
        // Thử parse trực tiếp trước
        classification = JSON.parse(text);
      } catch (e1) {
        try {
          // Tìm JSON trong text
          const jsonMatch = text.match(/\{[\s\S]*\}/);
          if (jsonMatch) {
            classification = JSON.parse(jsonMatch[0]);
          } else {
            throw new Error('No JSON found');
          }
        } catch (e2) {
          // Fallback nhanh
          classification = this.createFallbackResult(text);
        }
      }

      // Validate nhanh
      const validatedResult = this.validateResult(classification);

      const processingTime = Date.now() - startTime;

      // Update performance stats
      this.performanceStats.successfulRequests++;
      this.performanceStats.averageResponseTime =
        (this.performanceStats.averageResponseTime * (this.performanceStats.successfulRequests - 1) + processingTime) /
        this.performanceStats.successfulRequests;

      const finalResult = {
        success: true,
        data: validatedResult,
        processingTime,
        modelUsed: 'gemini-1.5-flash'
      };

      // Cache kết quả
      this.resultCache.set(fileHash, finalResult);

      return finalResult;

    } catch (error) {
      this.activeRequests = Math.max(0, this.activeRequests - 1);

      if (error.message.includes('quota') ||
          error.message.includes('rate') ||
          error.message.includes('limit') ||
          error.status === 429) {

        this.isRateLimited = true;
        return {
          success: false,
          error: `Rate limit. Đợi ${Math.ceil((this.rateLimitResetTime - Date.now()) / 1000)}s`,
          retryAfter: Math.max(3000, this.rateLimitResetTime - Date.now()),
          isRateLimited: true,
          canRetry: true
        };
      }

      return {
        success: false,
        error: error.message || 'Lỗi không xác định',
        processingTime: Date.now() - startTime
      };
    }
  }

  /**
   * Tạo kết quả fallback nhanh
   */
  createFallbackResult(text) {
    const categories = ['Bó hoa', 'Giỏ hoa', 'Bình hoa', 'Lẵng hoa', 'Vòng hoa', 'Hoa cài', 'Khác'];
    let category = 'Khác';
    let confidence = 60;

    // Tìm category nhanh
    for (const cat of categories) {
      if (text.toLowerCase().includes(cat.toLowerCase())) {
        category = cat;
        confidence = 75;
        break;
      }
    }

    return {
      category,
      confidence,
      description: 'Phân loại tự động từ text',
      flowers: [],
      colors: [],
      reasoning: 'Fallback classification'
    };
  }

  /**
   * Validate kết quả nhanh
   */
  validateResult(classification) {
    const validCategories = ['Bó hoa', 'Giỏ hoa', 'Bình hoa', 'Lẵng hoa', 'Vòng hoa', 'Hoa cài', 'Khác'];

    if (!validCategories.includes(classification.category)) {
      classification.category = 'Khác';
      classification.confidence = Math.max(0, (classification.confidence || 50) - 20);
    }

    classification.confidence = Math.max(0, Math.min(100, classification.confidence || 50));
    classification.description = classification.description || 'Không có mô tả';
    classification.flowers = Array.isArray(classification.flowers) ? classification.flowers : [];
    classification.colors = Array.isArray(classification.colors) ? classification.colors : [];
    classification.reasoning = classification.reasoning || 'Phân loại tự động';

    return classification;
  }

  /**
   * Xử lý song song với queue management thông minh
   */
  async classifyMultipleImages(files, onProgress = null, options = {}) {
    const {
      maxConcurrent = 3, // Tăng lên 3 cho Flash model
      retryAttempts = 2,
      useParallel = true
    } = options;

    const validFiles = files.filter(file => this.isValidImageFile(file));
    const results = [];
    const startTime = Date.now();

    if (useParallel && validFiles.length > 1) {
      // Xử lý song song với batching thông minh
      const batches = this.createOptimalBatches(validFiles, maxConcurrent);

      for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
        const batch = batches[batchIndex];

        // Xử lý song song trong batch
        const batchPromises = batch.map(async (file, fileIndex) => {
          const globalIndex = batchIndex * maxConcurrent + fileIndex;

          if (onProgress) {
            onProgress({
              current: globalIndex + 1,
              total: validFiles.length,
              fileName: file.name,
              status: 'processing',
              batchInfo: {
                currentBatch: batchIndex + 1,
                totalBatches: batches.length,
                parallelProcessing: true
              }
            });
          }

          let result;
          for (let attempt = 0; attempt <= retryAttempts; attempt++) {
            try {
              result = await this.classifyFlowerImage(file);
              if (result.success || !result.canRetry) break;

              if (result.isRateLimited) {
                await new Promise(resolve => setTimeout(resolve, result.retryAfter));
              }
            } catch (error) {
              if (attempt === retryAttempts) {
                result = {
                  success: false,
                  error: error.message,
                  attempts: attempt + 1
                };
              }
            }
          }

          if (onProgress) {
            onProgress({
              current: globalIndex + 1,
              total: validFiles.length,
              fileName: file.name,
              status: result.success ? 'completed' : 'error',
              processingTime: result.processingTime
            });
          }

          return {
            file,
            fileName: file.name,
            result,
            processingTime: result.processingTime || 0
          };
        });

        // Đợi batch hoàn thành
        const batchResults = await Promise.all(batchPromises);
        results.push(...batchResults);

        // Intelligent delay giữa batches
        if (batchIndex < batches.length - 1) {
          const avgTime = batchResults.reduce((sum, r) => sum + r.processingTime, 0) / batchResults.length;
          const delay = Math.min(2000, Math.max(500, avgTime * 0.1));

          if (onProgress) {
            onProgress({
              current: (batchIndex + 1) * maxConcurrent,
              total: validFiles.length,
              status: 'waiting',
              waitTime: delay,
              message: `Đợi ${Math.ceil(delay / 1000)}s trước batch tiếp theo...`
            });
          }

          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    } else {
      // Sequential processing cho file ít hoặc khi parallel bị tắt
      for (let i = 0; i < validFiles.length; i++) {
        const file = validFiles[i];

        if (onProgress) {
          onProgress({
            current: i + 1,
            total: validFiles.length,
            fileName: file.name,
            status: 'processing',
            parallelProcessing: false
          });
        }

        let result;
        for (let attempt = 0; attempt <= retryAttempts; attempt++) {
          try {
            result = await this.classifyFlowerImage(file);
            if (result.success || !result.canRetry) break;

            if (result.isRateLimited) {
              await new Promise(resolve => setTimeout(resolve, result.retryAfter));
            }
          } catch (error) {
            if (attempt === retryAttempts) {
              result = {
                success: false,
                error: error.message,
                attempts: attempt + 1
              };
            }
          }
        }

        results.push({
          file,
          fileName: file.name,
          result,
          processingTime: result.processingTime || 0
        });

        if (onProgress) {
          onProgress({
            current: i + 1,
            total: validFiles.length,
            fileName: file.name,
            status: result.success ? 'completed' : 'error',
            processingTime: result.processingTime
          });
        }

        // Delay nhỏ giữa sequential requests
        if (i < validFiles.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }
    }

    // Tính toán thống kê cuối
    const totalTime = Date.now() - startTime;
    const successCount = results.filter(r => r.result.success).length;

    if (onProgress) {
      onProgress({
        current: validFiles.length,
        total: validFiles.length,
        status: 'completed',
        summary: {
          totalFiles: validFiles.length,
          successful: successCount,
          failed: validFiles.length - successCount,
          totalTime,
          avgTimePerFile: totalTime / validFiles.length,
          successRate: (successCount / validFiles.length) * 100,
          parallelProcessing: useParallel && validFiles.length > 1
        }
      });
    }

    return results;
  }

  /**
   * Tạo batches tối ưu cho parallel processing
   */
  createOptimalBatches(files, maxConcurrent) {
    const batches = [];
    for (let i = 0; i < files.length; i += maxConcurrent) {
      batches.push(files.slice(i, i + maxConcurrent));
    }
    return batches;
  }

  /**
   * Lấy thống kê hiệu suất
   */
  getPerformanceStats() {
    return {
      ...this.performanceStats,
      cacheHitRate: this.performanceStats.cacheHits / (this.performanceStats.cacheHits + this.performanceStats.cacheMisses) * 100,
      cacheSize: this.resultCache.size,
      rateLimitStatus: this.getRateLimitStatus()
    };
  }

  /**
   * Clear cache để giải phóng memory
   */
  clearCache() {
    this.cache.clear();
    this.imageHashCache.clear();
    this.resultCache.clear();
    this.performanceStats.cacheHits = 0;
    this.performanceStats.cacheMisses = 0;
  }

  /**
   * Reset performance stats
   */
  resetStats() {
    this.performanceStats = {
      totalRequests: 0,
      successfulRequests: 0,
      averageResponseTime: 0,
      cacheHits: 0,
      cacheMisses: 0
    };
  }
}

export default new GeminiService();
