import { GoogleGenerativeAI } from '@google/generative-ai';

// API Key - trong thực tế nên lưu trong environment variables
const API_KEY = 'AIzaSyAVq6hfli-gbIHrHxBmaInNiTLThPVC4o4';

class GeminiService {
  constructor() {
    this.genAI = new GoogleGenerativeAI(API_KEY);
    // Sử dụng model mạnh nhất cho độ chính xác cao
    this.model = this.genAI.getGenerativeModel({
      model: 'gemini-1.5-pro',
      generationConfig: {
        temperature: 0.1, // Giảm randomness để tăng consistency
        topK: 1,
        topP: 0.8,
        maxOutputTokens: 2048,
      },
      safetySettings: [
        {
          category: "HARM_CATEGORY_HARASSMENT",
          threshold: "BLOCK_MEDIUM_AND_ABOVE",
        },
        {
          category: "HARM_CATEGORY_HATE_SPEECH",
          threshold: "BLOCK_MEDIUM_AND_ABOVE",
        },
        {
          category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
          threshold: "BLOCK_MEDIUM_AND_ABOVE",
        },
        {
          category: "HARM_CATEGORY_DANGEROUS_CONTENT",
          threshold: "BLOCK_MEDIUM_AND_ABOVE",
        },
      ],
    });

    // Cache cho kết quả đã xử lý
    this.cache = new Map();
    this.processingQueue = [];
    this.isProcessing = false;
  }

  /**
   * Kiểm tra xem API key đã được cấu hình chưa
   * @returns {boolean} - True nếu API key đã được cấu hình
   */
  isConfigured() {
    return API_KEY && API_KEY !== 'YOUR_GEMINI_API_KEY_HERE' && API_KEY.length > 10;
  }

  /**
   * Tạo hash cho file để cache
   */
  async getFileHash(file) {
    const arrayBuffer = await file.arrayBuffer();
    const hashBuffer = await crypto.subtle.digest('SHA-256', arrayBuffer);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  }

  /**
   * Tiền xử lý hình ảnh để tối ưu hóa
   */
  async preprocessImage(file) {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        // Resize để tối ưu tốc độ mà vẫn giữ chất lượng
        const maxSize = 1024;
        let { width, height } = img;

        if (width > height && width > maxSize) {
          height = (height * maxSize) / width;
          width = maxSize;
        } else if (height > maxSize) {
          width = (width * maxSize) / height;
          height = maxSize;
        }

        canvas.width = width;
        canvas.height = height;

        // Enhance contrast và sharpness
        ctx.filter = 'contrast(110%) brightness(105%)';
        ctx.drawImage(img, 0, 0, width, height);

        canvas.toBlob(resolve, 'image/jpeg', 0.9);
      };

      img.src = URL.createObjectURL(file);
    });
  }

  /**
   * Phân loại hình ảnh hoa với AI nâng cao
   * @param {File} imageFile - File hình ảnh cần phân loại
   * @returns {Promise<Object>} - Kết quả phân loại
   */
  async classifyFlowerImage(imageFile) {
    try {
      // Kiểm tra cache
      const fileHash = await this.getFileHash(imageFile);
      if (this.cache.has(fileHash)) {
        return this.cache.get(fileHash);
      }

      // Tiền xử lý hình ảnh
      const processedFile = await this.preprocessImage(imageFile);
      const imageData = await this.fileToGenerativePart(processedFile);

      // Advanced prompt engineering cho độ chính xác cao
      const prompt = `
Bạn là chuyên gia phân loại hoa với 20 năm kinh nghiệm. Hãy phân tích hình ảnh này một cách chi tiết và chính xác.

NHIỆM VỤ: Xác định loại cách sắp xếp/trình bày hoa trong hình ảnh.

PHÂN TÍCH THEO CÁC BƯỚC:
1. Quan sát container/vật chứa hoa
2. Xác định cách sắp xếp và trình bày
3. Đánh giá độ tin cậy dựa trên các đặc điểm rõ ràng

DANH MỤC PHÂN LOẠI (chọn 1):
- "Bó hoa": Hoa được buộc/cột thành bó, có thể có giấy gói, ruy băng, hoặc dây buộc. Thường cầm tay được.
- "Giỏ hoa": Hoa được cắm/sắp xếp trong giỏ, rổ, thúng (làm từ mây, tre, nhựa, kim loại)
- "Bình hoa": Hoa được cắm trong bình, lọ, chậu, ly, hoặc bất kỳ vật chứa nước nào
- "Lẵng hoa": Hoa được sắp xếp trong lẵng (thường lớn, dùng cho khai trương, tang lễ)
- "Vòng hoa": Hoa được làm thành vòng tròn (vòng đeo cổ, vòng trang trí)
- "Hoa cài": Hoa nhỏ để cài áo, tóc, hoặc trang trí cá nhân
- "Khác": Các cách sắp xếp không thuộc các loại trên

CÁC YẾU TỐ ĐÁNH GIÁ ĐỘ TIN CẬY:
- Container rõ ràng: +20 điểm
- Cách sắp xếp đặc trưng: +25 điểm
- Hoa rõ nét, không bị che khuất: +20 điểm
- Góc chụp tốt: +15 điểm
- Ánh sáng đủ: +10 điểm
- Không có vật cản: +10 điểm

ĐỊNH DẠNG TRẢ VỀ (JSON thuần, không markdown):
{
  "category": "tên danh mục chính xác",
  "confidence": số từ 0-100,
  "description": "mô tả chi tiết cách sắp xếp và container",
  "flowers": ["danh sách loại hoa nhận diện được"],
  "colors": ["màu sắc chủ đạo"],
  "container": "mô tả vật chứa/cách trình bày",
  "style": "phong cách sắp xếp (formal/casual/artistic/etc)",
  "occasion": "dịp sử dụng có thể (wedding/birthday/funeral/etc)",
  "reasoning": "lý do phân loại vào danh mục này"
}

Hãy phân tích cẩn thận và trả về JSON chính xác.
      `;

      const result = await this.model.generateContent([prompt, imageData]);
      const response = await result.response;
      const text = response.text();

      // Advanced JSON parsing với fallback
      let classification;
      try {
        // Thử parse trực tiếp
        classification = JSON.parse(text);
      } catch (e1) {
        try {
          // Tìm JSON trong text
          const jsonMatch = text.match(/\{[\s\S]*\}/);
          if (jsonMatch) {
            classification = JSON.parse(jsonMatch[0]);
          } else {
            throw new Error('No JSON found');
          }
        } catch (e2) {
          // Fallback: tạo kết quả từ text analysis
          classification = this.parseTextToClassification(text, imageFile.name);
        }
      }

      // Validate và enhance kết quả
      const validatedResult = this.validateAndEnhanceResult(classification);

      // Cache kết quả
      const finalResult = {
        success: true,
        data: validatedResult,
        processingTime: Date.now() - startTime,
        modelUsed: 'gemini-1.5-pro'
      };

      this.cache.set(fileHash, finalResult);
      return finalResult;

    } catch (error) {
      console.error('Lỗi khi gọi Gemini API:', error);

      // Retry logic với exponential backoff
      if (error.message.includes('quota') || error.message.includes('rate')) {
        return {
          success: false,
          error: 'Đã vượt quá giới hạn API. Vui lòng thử lại sau.',
          retryAfter: 60000 // 1 minute
        };
      }

      return {
        success: false,
        error: error.message || 'Lỗi không xác định khi phân loại hình ảnh',
        processingTime: Date.now() - startTime
      };
    }
  }

  /**
   * Parse text response thành classification khi JSON parsing thất bại
   */
  parseTextToClassification(text, fileName) {
    const categories = ['Bó hoa', 'Giỏ hoa', 'Bình hoa', 'Lẵng hoa', 'Vòng hoa', 'Hoa cài', 'Khác'];

    // Tìm category trong text
    let category = 'Khác';
    let confidence = 50;

    for (const cat of categories) {
      if (text.toLowerCase().includes(cat.toLowerCase())) {
        category = cat;
        confidence = 75;
        break;
      }
    }

    return {
      category,
      confidence,
      description: `Phân loại dựa trên text analysis: ${text.substring(0, 100)}...`,
      flowers: this.extractFlowersFromText(text),
      colors: this.extractColorsFromText(text),
      container: 'Không xác định',
      style: 'Không xác định',
      occasion: 'Không xác định',
      reasoning: 'Fallback classification từ text analysis'
    };
  }

  /**
   * Validate và enhance kết quả classification
   */
  validateAndEnhanceResult(classification) {
    const validCategories = ['Bó hoa', 'Giỏ hoa', 'Bình hoa', 'Lẵng hoa', 'Vòng hoa', 'Hoa cài', 'Khác'];

    // Validate category
    if (!validCategories.includes(classification.category)) {
      classification.category = 'Khác';
      classification.confidence = Math.max(0, (classification.confidence || 50) - 20);
    }

    // Validate confidence
    classification.confidence = Math.max(0, Math.min(100, classification.confidence || 50));

    // Ensure required fields
    classification.description = classification.description || 'Không có mô tả';
    classification.flowers = Array.isArray(classification.flowers) ? classification.flowers : [];
    classification.colors = Array.isArray(classification.colors) ? classification.colors : [];
    classification.container = classification.container || 'Không xác định';
    classification.style = classification.style || 'Không xác định';
    classification.occasion = classification.occasion || 'Không xác định';
    classification.reasoning = classification.reasoning || 'Phân loại tự động';

    // Enhance confidence based on completeness
    const completenessScore = this.calculateCompletenessScore(classification);
    classification.confidence = Math.min(100, classification.confidence + completenessScore);

    return classification;
  }

  /**
   * Tính điểm completeness để adjust confidence
   */
  calculateCompletenessScore(classification) {
    let score = 0;

    if (classification.flowers && classification.flowers.length > 0) score += 5;
    if (classification.colors && classification.colors.length > 0) score += 5;
    if (classification.container && classification.container !== 'Không xác định') score += 5;
    if (classification.description && classification.description.length > 20) score += 5;
    if (classification.style && classification.style !== 'Không xác định') score += 3;
    if (classification.occasion && classification.occasion !== 'Không xác định') score += 2;

    return score;
  }

  /**
   * Extract flower names từ text
   */
  extractFlowersFromText(text) {
    const commonFlowers = [
      'hoa hồng', 'hoa cúc', 'hoa ly', 'hoa tulip', 'hoa lan', 'hoa hướng dương',
      'hoa cẩm chướng', 'hoa baby', 'hoa đồng tiền', 'hoa cúc họa mi',
      'hoa sen', 'hoa đào', 'hoa mai', 'hoa anh đào', 'hoa lavender'
    ];

    const found = [];
    const lowerText = text.toLowerCase();

    for (const flower of commonFlowers) {
      if (lowerText.includes(flower)) {
        found.push(flower.charAt(0).toUpperCase() + flower.slice(1));
      }
    }

    return found;
  }

  /**
   * Extract colors từ text
   */
  extractColorsFromText(text) {
    const commonColors = [
      'đỏ', 'xanh', 'vàng', 'trắng', 'hồng', 'tím', 'cam', 'xanh lá',
      'xanh dương', 'nâu', 'đen', 'kem', 'bạc', 'vàng gold'
    ];

    const found = [];
    const lowerText = text.toLowerCase();

    for (const color of commonColors) {
      if (lowerText.includes(color)) {
        found.push(color.charAt(0).toUpperCase() + color.slice(1));
      }
    }

    return found;
  }

  /**
   * Chuyển đổi file thành format cho Gemini API
   * @param {File} file - File cần chuyển đổi
   * @returns {Promise<Object>} - Object chứa data và mimeType
   */
  async fileToGenerativePart(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const base64Data = reader.result.split(',')[1];
        resolve({
          inlineData: {
            data: base64Data,
            mimeType: file.type
          }
        });
      };
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  }

  /**
   * Kiểm tra xem file có phải là hình ảnh hợp lệ không
   * @param {File} file - File cần kiểm tra
   * @returns {boolean} - True nếu là hình ảnh hợp lệ
   */
  isValidImageFile(file) {
    const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    const maxSize = 10 * 1024 * 1024; // 10MB
    
    return validTypes.includes(file.type) && file.size <= maxSize;
  }

  /**
   * Advanced batch processing với parallel processing và queue management
   * @param {File[]} files - Danh sách files cần xử lý
   * @param {Function} onProgress - Callback để báo cáo tiến độ
   * @param {Object} options - Tùy chọn xử lý
   * @returns {Promise<Array>} - Danh sách kết quả
   */
  async classifyMultipleImages(files, onProgress = null, options = {}) {
    const {
      maxConcurrent = 3, // Số lượng xử lý song song
      retryAttempts = 2,
      prioritizeBySize = true,
      useSmartBatching = true
    } = options;

    const validFiles = files.filter(file => this.isValidImageFile(file));

    // Sắp xếp files theo kích thước nếu được yêu cầu
    if (prioritizeBySize) {
      validFiles.sort((a, b) => a.size - b.size); // Xử lý file nhỏ trước
    }

    // Smart batching: chia thành các batch tối ưu
    const batches = useSmartBatching ?
      this.createSmartBatches(validFiles, maxConcurrent) :
      this.createEqualBatches(validFiles, maxConcurrent);

    const results = [];
    let processedCount = 0;
    const startTime = Date.now();

    for (const batch of batches) {
      // Xử lý song song trong batch
      const batchPromises = batch.map(async (file, index) => {
        const fileStartTime = Date.now();

        if (onProgress) {
          onProgress({
            current: processedCount + index + 1,
            total: validFiles.length,
            fileName: file.name,
            status: 'processing',
            batchInfo: {
              currentBatch: batches.indexOf(batch) + 1,
              totalBatches: batches.length,
              filesInBatch: batch.length
            }
          });
        }

        let result;
        let attempts = 0;

        // Retry logic
        while (attempts <= retryAttempts) {
          try {
            result = await this.classifyFlowerImage(file);
            break;
          } catch (error) {
            attempts++;
            if (attempts > retryAttempts) {
              result = {
                success: false,
                error: error.message,
                attempts: attempts
              };
            } else {
              // Exponential backoff
              await new Promise(resolve =>
                setTimeout(resolve, Math.pow(2, attempts) * 1000)
              );
            }
          }
        }

        const processingTime = Date.now() - fileStartTime;

        if (onProgress) {
          onProgress({
            current: processedCount + index + 1,
            total: validFiles.length,
            fileName: file.name,
            status: result.success ? 'completed' : 'error',
            processingTime,
            batchInfo: {
              currentBatch: batches.indexOf(batch) + 1,
              totalBatches: batches.length,
              filesInBatch: batch.length
            }
          });
        }

        return {
          file: file,
          fileName: file.name,
          result: result,
          processingTime,
          attempts
        };
      });

      // Chờ batch hoàn thành
      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);
      processedCount += batch.length;

      // Adaptive delay giữa các batch
      if (batches.indexOf(batch) < batches.length - 1) {
        const avgProcessingTime = batchResults.reduce((sum, r) => sum + r.processingTime, 0) / batchResults.length;
        const adaptiveDelay = Math.min(2000, Math.max(500, avgProcessingTime * 0.1));
        await new Promise(resolve => setTimeout(resolve, adaptiveDelay));
      }
    }

    // Tính toán thống kê cuối
    const totalTime = Date.now() - startTime;
    const successCount = results.filter(r => r.result.success).length;

    if (onProgress) {
      onProgress({
        current: validFiles.length,
        total: validFiles.length,
        status: 'completed',
        summary: {
          totalFiles: validFiles.length,
          successful: successCount,
          failed: validFiles.length - successCount,
          totalTime,
          avgTimePerFile: totalTime / validFiles.length,
          successRate: (successCount / validFiles.length) * 100
        }
      });
    }

    return results;
  }

  /**
   * Tạo smart batches dựa trên kích thước file
   */
  createSmartBatches(files, maxConcurrent) {
    const batches = [];
    const targetBatchSize = Math.ceil(files.length / Math.ceil(files.length / maxConcurrent));

    for (let i = 0; i < files.length; i += targetBatchSize) {
      batches.push(files.slice(i, i + targetBatchSize));
    }

    return batches;
  }

  /**
   * Tạo equal batches
   */
  createEqualBatches(files, maxConcurrent) {
    const batches = [];
    for (let i = 0; i < files.length; i += maxConcurrent) {
      batches.push(files.slice(i, i + maxConcurrent));
    }
    return batches;
  }

  /**
   * Phân tích chất lượng hình ảnh trước khi xử lý
   */
  async analyzeImageQuality(file) {
    return new Promise((resolve) => {
      const img = new Image();
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      img.onload = () => {
        canvas.width = img.width;
        canvas.height = img.height;
        ctx.drawImage(img, 0, 0);

        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        const data = imageData.data;

        // Tính toán các metrics chất lượng
        let brightness = 0;
        let contrast = 0;
        let sharpness = 0;

        // Brightness calculation
        for (let i = 0; i < data.length; i += 4) {
          brightness += (data[i] + data[i + 1] + data[i + 2]) / 3;
        }
        brightness = brightness / (data.length / 4);

        // Simple contrast calculation
        let variance = 0;
        for (let i = 0; i < data.length; i += 4) {
          const gray = (data[i] + data[i + 1] + data[i + 2]) / 3;
          variance += Math.pow(gray - brightness, 2);
        }
        contrast = Math.sqrt(variance / (data.length / 4));

        // Quality score
        const qualityScore = Math.min(100,
          (brightness > 50 && brightness < 200 ? 30 : 10) +
          (contrast > 20 ? 30 : 10) +
          (img.width >= 300 && img.height >= 300 ? 25 : 10) +
          (file.size < 5 * 1024 * 1024 ? 15 : 5) // < 5MB
        );

        resolve({
          width: img.width,
          height: img.height,
          brightness: Math.round(brightness),
          contrast: Math.round(contrast),
          qualityScore: Math.round(qualityScore),
          recommendations: this.getQualityRecommendations(brightness, contrast, img.width, img.height)
        });
      };

      img.src = URL.createObjectURL(file);
    });
  }

  /**
   * Đưa ra khuyến nghị cải thiện chất lượng
   */
  getQualityRecommendations(brightness, contrast, width, height) {
    const recommendations = [];

    if (brightness < 50) recommendations.push('Hình ảnh quá tối, nên tăng độ sáng');
    if (brightness > 200) recommendations.push('Hình ảnh quá sáng, nên giảm độ sáng');
    if (contrast < 20) recommendations.push('Độ tương phản thấp, nên tăng contrast');
    if (width < 300 || height < 300) recommendations.push('Độ phân giải thấp, nên sử dụng ảnh có độ phân giải cao hơn');

    if (recommendations.length === 0) {
      recommendations.push('Chất lượng hình ảnh tốt');
    }

    return recommendations;
  }

  /**
   * Tự động cải thiện chất lượng hình ảnh
   */
  async enhanceImageQuality(file, qualityAnalysis) {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        canvas.width = img.width;
        canvas.height = img.height;

        // Auto-enhance dựa trên quality analysis
        let filters = [];

        if (qualityAnalysis.brightness < 80) {
          filters.push('brightness(120%)');
        } else if (qualityAnalysis.brightness > 180) {
          filters.push('brightness(80%)');
        }

        if (qualityAnalysis.contrast < 30) {
          filters.push('contrast(130%)');
        }

        // Luôn thêm một chút sharpening
        filters.push('contrast(105%)');
        filters.push('saturate(110%)');

        ctx.filter = filters.join(' ');
        ctx.drawImage(img, 0, 0);

        canvas.toBlob(resolve, 'image/jpeg', 0.92);
      };

      img.src = URL.createObjectURL(file);
    });
  }

  /**
   * Tạo thumbnail cho preview nhanh
   */
  async createThumbnail(file, maxSize = 150) {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        const ratio = Math.min(maxSize / img.width, maxSize / img.height);
        canvas.width = img.width * ratio;
        canvas.height = img.height * ratio;

        ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
        canvas.toBlob(resolve, 'image/jpeg', 0.8);
      };

      img.src = URL.createObjectURL(file);
    });
  }

  /**
   * Xuất báo cáo chi tiết
   */
  generateDetailedReport(results) {
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        totalImages: results.length,
        successful: results.filter(r => r.result.success).length,
        failed: results.filter(r => !r.result.success).length,
        avgConfidence: 0,
        avgProcessingTime: 0
      },
      categoryBreakdown: {},
      qualityMetrics: {
        highQuality: 0,
        mediumQuality: 0,
        lowQuality: 0
      },
      recommendations: [],
      detailedResults: results
    };

    // Tính toán metrics
    const successfulResults = results.filter(r => r.result.success);

    if (successfulResults.length > 0) {
      report.summary.avgConfidence = successfulResults.reduce((sum, r) =>
        sum + (r.result.data?.confidence || 0), 0) / successfulResults.length;

      report.summary.avgProcessingTime = results.reduce((sum, r) =>
        sum + (r.processingTime || 0), 0) / results.length;

      // Category breakdown
      successfulResults.forEach(r => {
        const category = r.result.data?.category || 'Khác';
        report.categoryBreakdown[category] = (report.categoryBreakdown[category] || 0) + 1;
      });

      // Quality metrics
      successfulResults.forEach(r => {
        const confidence = r.result.data?.confidence || 0;
        if (confidence >= 80) report.qualityMetrics.highQuality++;
        else if (confidence >= 60) report.qualityMetrics.mediumQuality++;
        else report.qualityMetrics.lowQuality++;
      });
    }

    // Recommendations
    if (report.summary.avgConfidence < 70) {
      report.recommendations.push('Nên sử dụng hình ảnh chất lượng cao hơn để tăng độ chính xác');
    }

    if (report.qualityMetrics.lowQuality > report.summary.successful * 0.3) {
      report.recommendations.push('Nhiều hình ảnh có độ tin cậy thấp, kiểm tra chất lượng ảnh đầu vào');
    }

    return report;
  }

  /**
   * Clear cache để giải phóng memory
   */
  clearCache() {
    this.cache.clear();
  }

  /**
   * Lấy thống kê cache
   */
  getCacheStats() {
    return {
      size: this.cache.size,
      memoryUsage: JSON.stringify([...this.cache.values()]).length,
      hitRate: this.cacheHits / (this.cacheHits + this.cacheMisses) || 0
    };
  }
}

export default new GeminiService();
