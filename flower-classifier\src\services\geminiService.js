import { GoogleGenerativeAI } from '@google/generative-ai';

// API Key
const API_KEY = 'AIzaSyAVq6hfli-gbIHrHxBmaInNiTLThPVC4o4';

class GeminiService {
  constructor() {
    this.genAI = new GoogleGenerativeAI(API_KEY);
    
    // Sử dụng Gemini Flash cho tốc độ tối đa
    this.model = this.genAI.getGenerativeModel({ 
      model: 'gemini-1.5-flash',
      generationConfig: {
        temperature: 0.1,
        topK: 1,
        topP: 0.8,
        maxOutputTokens: 1024,
        candidateCount: 1,
      },
      safetySettings: [
        {
          category: "HARM_CATEGORY_HARASSMENT",
          threshold: "BLOCK_MEDIUM_AND_ABOVE",
        },
        {
          category: "HARM_CATEGORY_HATE_SPEECH", 
          threshold: "BLOCK_MEDIUM_AND_ABOVE",
        },
        {
          category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
          threshold: "BLOCK_MEDIUM_AND_ABOVE",
        },
        {
          category: "HARM_CATEGORY_DANGEROUS_CONTENT",
          threshold: "BLOCK_MEDIUM_AND_ABOVE",
        },
      ],
    });
    
    // Caching system
    this.cache = new Map();
    this.resultCache = new Map();
    
    // Rate limiting - Tối ưu cho xử lý song song
    this.requestCount = 0;
    this.requestWindow = 60000; // 1 minute
    this.maxRequestsPerMinute = 15; // Tăng lên 15 cho xử lý song song
    this.lastRequestTime = 0;
    this.isRateLimited = false;

    // Daily quota tracking
    this.dailyQuota = 50; // Free tier limit
    this.dailyUsed = parseInt(localStorage.getItem('gemini_daily_used') || '0');
    this.quotaResetDate = localStorage.getItem('gemini_quota_reset') || new Date().toDateString();
    
    // Performance stats
    this.performanceStats = {
      totalRequests: 0,
      successfulRequests: 0,
      averageResponseTime: 0,
      cacheHits: 0,
      cacheMisses: 0
    };
  }

  /**
   * Kiểm tra API key
   */
  isConfigured() {
    return API_KEY && API_KEY !== 'YOUR_GEMINI_API_KEY_HERE' && API_KEY.length > 10;
  }

  /**
   * Kiểm tra daily quota
   */
  checkDailyQuota() {
    const today = new Date().toDateString();

    // Reset quota nếu sang ngày mới
    if (this.quotaResetDate !== today) {
      this.dailyUsed = 0;
      this.quotaResetDate = today;
      localStorage.setItem('gemini_daily_used', '0');
      localStorage.setItem('gemini_quota_reset', today);
    }

    return this.dailyUsed < this.dailyQuota;
  }

  /**
   * Increment daily usage
   */
  incrementDailyUsage() {
    this.dailyUsed++;
    localStorage.setItem('gemini_daily_used', this.dailyUsed.toString());
  }

  /**
   * Get quota status
   */
  getQuotaStatus() {
    return {
      used: this.dailyUsed,
      total: this.dailyQuota,
      remaining: this.dailyQuota - this.dailyUsed,
      resetDate: this.quotaResetDate
    };
  }

  /**
   * Tạo hash nhanh cho file
   */
  async getFileHashFast(file) {
    return `${file.name}_${file.size}_${file.lastModified}_${file.type}`;
  }

  /**
   * Tiền xử lý ảnh tối ưu
   */
  async preprocessImageFast(file) {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();
      
      img.onload = () => {
        const maxSize = 800;
        let { width, height } = img;
        
        if (width > height && width > maxSize) {
          height = (height * maxSize) / width;
          width = maxSize;
        } else if (height > maxSize) {
          width = (width * maxSize) / height;
          height = maxSize;
        }
        
        canvas.width = width;
        canvas.height = height;
        
        ctx.imageSmoothingEnabled = true;
        ctx.imageSmoothingQuality = 'medium';
        ctx.drawImage(img, 0, 0, width, height);
        
        canvas.toBlob(resolve, 'image/jpeg', 0.85);
      };
      
      img.src = URL.createObjectURL(file);
    });
  }

  /**
   * Chuyển đổi file cho Gemini API
   */
  async fileToGenerativePart(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const base64Data = reader.result.split(',')[1];
        resolve({
          inlineData: {
            data: base64Data,
            mimeType: file.type
          }
        });
      };
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  }

  /**
   * Kiểm tra file hợp lệ
   */
  isValidImageFile(file) {
    const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    const maxSize = 10 * 1024 * 1024; // 10MB
    return validTypes.includes(file.type) && file.size <= maxSize;
  }

  /**
   * Rate limiting
   */
  checkRateLimit() {
    const now = Date.now();

    // Reset counter nếu đã qua window
    if (now - this.lastRequestTime > this.requestWindow) {
      console.log(`🔄 Rate limit reset: ${this.requestCount} -> 0`);
      this.requestCount = 0;
      this.lastRequestTime = now;
      this.isRateLimited = false;
    }

    // Kiểm tra có vượt giới hạn không
    if (this.requestCount >= this.maxRequestsPerMinute) {
      console.log(`⚠️ Rate limit reached: ${this.requestCount}/${this.maxRequestsPerMinute}`);
      this.isRateLimited = true;
      return false;
    }

    console.log(`✅ Rate limit OK: ${this.requestCount}/${this.maxRequestsPerMinute}`);
    return true;
  }

  /**
   * Đợi rate limit reset
   */
  async waitForRateLimit() {
    if (!this.isRateLimited) return;
    
    const waitTime = this.requestWindow;
    await new Promise(resolve => setTimeout(resolve, waitTime));
    
    this.isRateLimited = false;
    this.requestCount = 0;
  }

  /**
   * Lấy trạng thái rate limit
   */
  getRateLimitStatus() {
    return {
      isRateLimited: this.isRateLimited,
      requestCount: this.requestCount,
      maxRequests: this.maxRequestsPerMinute,
      timeUntilReset: this.isRateLimited ? this.requestWindow : 0
    };
  }

  /**
   * Prompt tối ưu
   */
  getOptimizedPrompt() {
    return `Phân tích hình ảnh hoa và trả về JSON:

PHÂN LOẠI:
- "Bó hoa": Hoa buộc thành bó, có giấy gói/ruy băng
- "Giỏ hoa": Hoa trong giỏ/rổ/thúng  
- "Bình hoa": Hoa trong bình/lọ/chậu
- "Lẵng hoa": Lẵng hoa lớn (khai trương/tang lễ)
- "Vòng hoa": Hoa làm thành vòng tròn
- "Hoa cài": Hoa nhỏ cài áo/tóc
- "Khác": Không thuộc loại trên

JSON (chỉ trả JSON):
{
  "category": "tên danh mục",
  "confidence": số 0-100,
  "description": "mô tả ngắn",
  "flowers": ["loại hoa"],
  "colors": ["màu chủ đạo"],
  "reasoning": "lý do phân loại"
}`;
  }

  /**
   * Tạo kết quả dự phòng
   */
  createFallbackResult(text) {
    return {
      category: "Khác",
      confidence: 50,
      description: "Không thể phân loại chính xác",
      flowers: ["Không xác định"],
      colors: ["Không xác định"],
      reasoning: "Lỗi phân tích: " + text.substring(0, 100)
    };
  }

  /**
   * Validate kết quả
   */
  validateResult(result) {
    const validCategories = ["Bó hoa", "Giỏ hoa", "Bình hoa", "Lẵng hoa", "Vòng hoa", "Hoa cài", "Khác"];

    return {
      category: validCategories.includes(result.category) ? result.category : "Khác",
      confidence: Math.min(Math.max(result.confidence || 50, 0), 100),
      description: result.description || "Không có mô tả",
      flowers: Array.isArray(result.flowers) ? result.flowers : ["Không xác định"],
      colors: Array.isArray(result.colors) ? result.colors : ["Không xác định"],
      reasoning: result.reasoning || "Không có lý do"
    };
  }

  /**
   * Phân loại hình ảnh siêu nhanh
   */
  async classifyFlowerImage(imageFile) {
    const startTime = Date.now();
    this.performanceStats.totalRequests++;

    try {
      // Kiểm tra file hợp lệ
      if (!this.isValidImageFile(imageFile)) {
        throw new Error('File không hợp lệ hoặc quá lớn');
      }

      // Kiểm tra cache
      const fileHash = await this.getFileHashFast(imageFile);
      if (this.resultCache.has(fileHash)) {
        this.performanceStats.cacheHits++;
        const cachedResult = this.resultCache.get(fileHash);
        return {
          ...cachedResult,
          fromCache: true,
          processingTime: Date.now() - startTime
        };
      }

      this.performanceStats.cacheMisses++;

      // Tiền xử lý nhanh
      const processedFile = await this.preprocessImageFast(imageFile);
      const imageData = await this.fileToGenerativePart(processedFile);

      // Tạm thời bỏ qua rate limit check để test
      // if (!this.checkRateLimit()) {
      //   await this.waitForRateLimit();
      // }

      // Increment counters
      this.requestCount++;
      this.lastRequestTime = Date.now();

      console.log(`📊 Request count: ${this.requestCount}/${this.maxRequestsPerMinute}`);

      // Gọi API
      const result = await this.model.generateContent([
        this.getOptimizedPrompt(),
        imageData
      ]);

      const response = await result.response;
      const text = response.text();

      // Parse JSON
      let classification;
      try {
        classification = JSON.parse(text);
      } catch (e1) {
        try {
          const jsonMatch = text.match(/\{[\s\S]*\}/);
          if (jsonMatch) {
            classification = JSON.parse(jsonMatch[0]);
          } else {
            throw new Error('No JSON found');
          }
        } catch (e2) {
          classification = this.createFallbackResult(text);
        }
      }

      // Validate
      const validatedResult = this.validateResult(classification);

      const processingTime = Date.now() - startTime;

      // Update stats
      this.performanceStats.successfulRequests++;
      this.performanceStats.averageResponseTime =
        (this.performanceStats.averageResponseTime * (this.performanceStats.successfulRequests - 1) + processingTime) /
        this.performanceStats.successfulRequests;

      const finalResult = {
        success: true,
        data: validatedResult,
        processingTime,
        modelUsed: 'gemini-1.5-flash'
      };

      // Cache kết quả
      this.resultCache.set(fileHash, finalResult);

      return finalResult;

    } catch (error) {
      if (error.message.includes('quota') ||
          error.message.includes('rate') ||
          error.message.includes('limit') ||
          error.status === 429) {

        this.isRateLimited = true;
        return {
          success: false,
          error: `Rate limit. Đợi 1 phút.`,
          retryAfter: 60000,
          isRateLimited: true,
          canRetry: true
        };
      }

      return {
        success: false,
        error: error.message || 'Lỗi không xác định',
        processingTime: Date.now() - startTime
      };
    }
  }

  /**
   * Lấy thống kê hiệu suất
   */
  getPerformanceStats() {
    return {
      ...this.performanceStats,
      cacheSize: this.resultCache.size,
      rateLimitStatus: this.getRateLimitStatus()
    };
  }

  /**
   * Phân loại nhiều hình ảnh với progress callback (xử lý song song)
   */
  async classifyMultipleImages(files, progressCallback) {
    const total = files.length;
    const batchSize = 2; // Giảm xuống 2 file cùng lúc để tránh rate limit
    const results = [];
    let completed = 0;

    // Chia files thành các batch
    const batches = [];
    for (let i = 0; i < files.length; i += batchSize) {
      batches.push(files.slice(i, i + batchSize));
    }

    console.log(`🚀 Xử lý ${total} files trong ${batches.length} batches (${batchSize} files/batch)`);

    // Xử lý từng batch
    for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
      const batch = batches[batchIndex];

      // Xử lý song song trong batch
      const batchPromises = batch.map(async (file, fileIndex) => {
        try {
          // Update progress cho file hiện tại
          if (progressCallback) {
            progressCallback({
              current: completed + fileIndex + 1,
              total: total,
              percentage: Math.round(((completed + fileIndex + 1) / total) * 100),
              currentFile: file.name,
              status: 'processing'
            });
          }

          // Classify với retry logic
          const result = await this.classifyWithRetry(file);

          return {
            file: file,
            result: result,
            success: true
          };

        } catch (error) {
          console.error(`Error classifying ${file.name}:`, error);
          return {
            file: file,
            result: null,
            success: false,
            error: error.message
          };
        }
      });

      // Chờ tất cả files trong batch hoàn thành
      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);
      completed += batch.length;

      // Update progress sau khi hoàn thành batch
      if (progressCallback) {
        progressCallback({
          current: completed,
          total: total,
          percentage: Math.round((completed / total) * 100),
          status: completed === total ? 'completed' : 'processing'
        });
      }

      // Delay giữa các batch để tránh overwhelm API
      if (batchIndex < batches.length - 1) {
        console.log(`⏳ Waiting 2s before next batch...`);
        await new Promise(resolve => setTimeout(resolve, 2000)); // Tăng delay
      }
    }

    console.log(`✅ Hoàn thành xử lý ${total} files`);
    return results;
  }

  /**
   * Classify single image với retry logic
   */
  async classifyWithRetry(file, maxRetries = 2) {
    let lastError;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        console.log(`🔍 Attempting to classify ${file.name} (attempt ${attempt + 1}/${maxRetries + 1})`);

        // Đơn giản hóa: chỉ gọi trực tiếp API
        const result = await this.classifyFlowerImage(file);
        console.log(`✅ Successfully classified ${file.name}`);
        return result;

      } catch (error) {
        lastError = error;
        console.log(`❌ Error classifying ${file.name}:`, error.message);

        if (error.message.includes('rate limit') || error.message.includes('quota') || error.message.includes('429')) {
          const waitTime = Math.min(2000 * (attempt + 1), 5000); // Tăng thời gian chờ
          console.log(`⏳ Rate limited, waiting ${waitTime}ms before retry...`);
          await new Promise(resolve => setTimeout(resolve, waitTime));
        } else if (attempt === maxRetries) {
          console.log(`💥 Max retries reached for ${file.name}`);
          throw error; // Lỗi khác và đã hết retry
        } else {
          // Chờ một chút trước khi retry cho lỗi khác
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }
    }

    throw lastError;
  }

  /**
   * Xóa cache
   */
  clearCache() {
    this.cache.clear();
    this.resultCache.clear();
    this.performanceStats.cacheHits = 0;
    this.performanceStats.cacheMisses = 0;
  }
}

export default new GeminiService();
