# ⚡ Hướng dẫn xử lý Rate Limit - Gemini API

## 🚨 Vấn đề Rate Limit

Khi bạn thấy lỗi **"Đã vượt quá giới hạn API"**, điều này có nghĩa là:

- Bạn đã gửi quá nhiều requests trong thời gian ngắn
- Gemini API Free Tier có giới hạn **15 requests/phút**
- Cần phải đợi trước khi tiếp tục xử lý

## 🔧 Giải pháp tức thì

### 1. Sử dụng Rate Limit Manager
1. **Click icon ⚡ (Zap)** trên header
2. **Xem trạng thái** rate limit hiện tại
3. **Đợi đến khi reset** hoặc **Reset thủ công**

### 2. Điều chỉnh cài đặt
- **Giảm số requests/phút** xuống 10-12
- **Tăng delay** g<PERSON><PERSON><PERSON> requests lên 5-6 giây
- **Bật Auto-retry** để tự động thử lại

### 3. Xử lý batch nhỏ
- **Upload 5-10 file** mỗi lần thay vì hàng trăm
- **Đợi hoàn thành** batch hiện tại trước khi upload batch mới

## 🛠️ Cấu hình tối ưu

### Cài đặt khuyến nghị cho Free Tier:
```
Max Requests/Minute: 12
Base Delay: 5000ms (5 giây)
Respect Rate Limit: ✅ Enabled
Auto Retry: ✅ Enabled
```

### Workflow tối ưu:
1. **Upload 5-8 hình ảnh** mỗi lần
2. **Đợi 2-3 phút** giữa các batch
3. **Theo dõi progress** và rate limit status
4. **Sử dụng cache** để tránh xử lý lại file trùng

## 📊 Hiểu về Gemini API Limits

### Free Tier Limits:
- **15 requests per minute** (RPM)
- **1,500 requests per day** (RPD)
- **1 million tokens per minute** (TPM)

### Paid Tier (nếu upgrade):
- **300 requests per minute**
- **50,000 requests per day**
- **4 million tokens per minute**

## 🚀 Tính năng Rate Limit Management

### Intelligent Rate Limiting
- **Tự động detect** khi gặp rate limit
- **Progressive delay** tăng dần thời gian đợi
- **Smart retry** với exponential backoff
- **Queue management** xử lý tuần tự

### Real-time Monitoring
- **Request counter** theo dõi số requests đã dùng
- **Time until reset** đếm ngược thời gian reset
- **Status indicators** hiển thị trạng thái trực quan
- **Progress tracking** với rate limit info

### Auto-Recovery
- **Automatic waiting** đợi tự động khi rate limited
- **Retry failed requests** thử lại requests thất bại
- **Fallback mechanisms** các phương án dự phòng
- **Error handling** xử lý lỗi thông minh

## 💡 Mẹo tối ưu hiệu suất

### 1. Batch Processing thông minh
```javascript
// Thay vì xử lý 50 file cùng lúc
processFiles(50files) // ❌ Sẽ bị rate limit

// Chia thành batch nhỏ
for (let i = 0; i < files.length; i += 5) {
  await processFiles(files.slice(i, i + 5)) // ✅ Tối ưu
  await delay(30000) // Đợi 30 giây giữa batch
}
```

### 2. Sử dụng Cache hiệu quả
- **File hash caching** tránh xử lý lại file trùng
- **Result caching** lưu kết quả đã phân loại
- **Smart invalidation** xóa cache khi cần thiết

### 3. Timing Strategy
- **Peak hours**: Tránh xử lý vào giờ cao điểm (9-17h)
- **Off-peak processing**: Xử lý vào tối/cuối tuần
- **Distributed processing**: Chia nhỏ công việc trong ngày

## 🔍 Troubleshooting

### Lỗi thường gặp:

#### "429 Too Many Requests"
**Nguyên nhân**: Vượt quá 15 requests/phút
**Giải pháp**:
1. Đợi 1-2 phút
2. Giảm tốc độ xử lý
3. Sử dụng Rate Limit Manager

#### "Quota exceeded"
**Nguyên nhân**: Vượt quá 1,500 requests/ngày
**Giải pháp**:
1. Đợi đến ngày hôm sau
2. Upgrade lên Paid tier
3. Tối ưu hóa requests

#### "Service unavailable"
**Nguyên nhân**: Gemini API tạm thời không khả dụng
**Giải pháp**:
1. Đợi 5-10 phút
2. Thử lại với delay lớn hơn
3. Kiểm tra status API

### Debug Steps:
1. **Mở Rate Limit Manager** để xem trạng thái
2. **Check console logs** để xem chi tiết lỗi
3. **Verify API key** đảm bảo key còn hoạt động
4. **Test với 1 file** trước khi xử lý batch

## 📈 Monitoring và Analytics

### Metrics quan trọng:
- **Success rate**: Tỷ lệ thành công
- **Average response time**: Thời gian phản hồi trung bình
- **Rate limit hits**: Số lần bị rate limit
- **Daily usage**: Sử dụng hàng ngày

### Alerts và Notifications:
- **Rate limit warning** khi gần đạt giới hạn
- **Daily quota alert** khi sử dụng 80% quota
- **Error notifications** khi có lỗi liên tục

## 🎯 Best Practices

### Cho người dùng cá nhân:
1. **Xử lý 5-10 file/lần** để tránh rate limit
2. **Sử dụng vào off-peak hours** (tối, cuối tuần)
3. **Enable auto-retry** để tự động xử lý lỗi
4. **Monitor daily usage** để không vượt quota

### Cho doanh nghiệp:
1. **Upgrade to Paid tier** để có limit cao hơn
2. **Implement queue system** xử lý hàng đợi
3. **Use multiple API keys** phân tán tải
4. **Schedule batch processing** xử lý theo lịch

## 🔄 Recovery Strategies

### Khi bị rate limited:
1. **Immediate**: Đợi 1-2 phút
2. **Short term**: Giảm tốc độ xử lý 50%
3. **Long term**: Tối ưu workflow và caching

### Khi vượt daily quota:
1. **Wait until reset** (midnight UTC)
2. **Upgrade API plan** nếu cần thiết
3. **Optimize requests** giảm số lượng calls

## 📞 Hỗ trợ

### Liên hệ khi cần:
- **GitHub Issues**: Báo lỗi rate limit
- **Email Support**: Hỗ trợ cấu hình
- **Documentation**: Tài liệu chi tiết

### Resources:
- [Gemini API Documentation](https://ai.google.dev/docs)
- [Rate Limiting Best Practices](https://cloud.google.com/apis/docs/capping-api-usage)
- [Quota Management](https://console.cloud.google.com/apis/api/generativelanguage.googleapis.com/quotas)

---

**💡 Lưu ý**: Rate limiting là cơ chế bảo vệ của Google để đảm bảo dịch vụ ổn định cho tất cả người dùng. Hãy tuân thủ các giới hạn để có trải nghiệm tốt nhất!
