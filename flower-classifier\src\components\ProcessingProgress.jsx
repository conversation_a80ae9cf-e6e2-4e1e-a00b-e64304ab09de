import React from 'react';
import { CheckCircle, XCircle, Clock, Image, AlertTriangle, Pause } from 'lucide-react';

const ProcessingProgress = ({ progress, results }) => {
  if (!progress && (!results || results.length === 0)) {
    return null;
  }

  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'error':
        return <XCircle className="h-5 w-5 text-red-500" />;
      case 'processing':
        return <Clock className="h-5 w-5 text-blue-500 animate-spin" />;
      default:
        return <Image className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'completed':
        return 'Hoàn thành';
      case 'error':
        return 'Lỗi';
      case 'processing':
        return 'Đang xử lý...';
      default:
        return 'Chờ xử lý';
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return 'text-green-600 bg-green-50';
      case 'error':
        return 'text-red-600 bg-red-50';
      case 'processing':
        return 'text-blue-600 bg-blue-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  return (
    <div className="w-full max-w-4xl mx-auto p-6">
      <div className="bg-white rounded-lg shadow-sm border">
        <div className="p-6 border-b">
          <h2 className="text-xl font-semibold text-gray-900">Tiến độ xử lý</h2>
          
          {progress && (
            <div className="mt-4">
              <div className="flex justify-between text-sm text-gray-600 mb-2">
                <span>
                  {progress.status === 'waiting' ? (
                    <span className="flex items-center">
                      <Pause className="h-4 w-4 mr-1 text-yellow-500" />
                      {progress.message || 'Đang đợi...'}
                    </span>
                  ) : (
                    `Đang xử lý: ${progress.fileName}`
                  )}
                </span>
                <span>{progress.current} / {progress.total}</span>
              </div>

              {/* Progress bar */}
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className={`h-2 rounded-full transition-all duration-300 ${
                    progress.status === 'waiting' ? 'bg-yellow-500' : 'bg-blue-600'
                  }`}
                  style={{ width: `${(progress.current / progress.total) * 100}%` }}
                ></div>
              </div>

              <div className="mt-2 flex justify-between items-center">
                <div className="text-sm text-gray-500">
                  {Math.round((progress.current / progress.total) * 100)}% hoàn thành
                </div>

                {/* Rate limit status */}
                {progress.rateLimitStatus && (
                  <div className="text-xs text-gray-500">
                    API: {progress.rateLimitStatus.requestCount}/{progress.rateLimitStatus.maxRequests}
                    {progress.rateLimitStatus.isRateLimited && (
                      <span className="text-yellow-600 ml-1">
                        (Rate Limited)
                      </span>
                    )}
                  </div>
                )}
              </div>

              {/* Wait time indicator */}
              {progress.waitTime && (
                <div className="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-sm">
                  <div className="flex items-center text-yellow-800">
                    <Clock className="h-4 w-4 mr-2" />
                    Đang đợi {Math.ceil(progress.waitTime / 1000)} giây để tránh vượt quá giới hạn API
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Danh sách kết quả */}
        {results && results.length > 0 && (
          <div className="p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Kết quả phân loại
            </h3>
            
            <div className="space-y-3">
              {results.map((item, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50"
                >
                  <div className="flex items-center space-x-3">
                    {getStatusIcon(item.result?.success ? 'completed' : 'error')}
                    
                    <div className="flex-1">
                      <div className="font-medium text-gray-900">
                        {item.fileName}
                      </div>
                      
                      {item.result?.success ? (
                        <div className="text-sm text-gray-600">
                          <span className="font-medium">
                            {item.result.data?.category || 'Không xác định'}
                          </span>
                          {item.result.data?.confidence && (
                            <span className="ml-2 text-gray-500">
                              ({item.result.data.confidence}% tin cậy)
                            </span>
                          )}
                        </div>
                      ) : (
                        <div className="text-sm text-red-600">
                          {item.result?.error || 'Lỗi không xác định'}
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div className={`px-3 py-1 rounded-full text-xs font-medium ${
                    getStatusColor(item.result?.success ? 'completed' : 'error')
                  }`}>
                    {getStatusText(item.result?.success ? 'completed' : 'error')}
                  </div>
                </div>
              ))}
            </div>
            
            {/* Thống kê tổng quan */}
            <div className="mt-6 p-4 bg-gray-50 rounded-lg">
              <h4 className="font-medium text-gray-900 mb-2">Thống kê</h4>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <div className="text-gray-500">Tổng số</div>
                  <div className="font-medium">{results.length}</div>
                </div>
                <div>
                  <div className="text-gray-500">Thành công</div>
                  <div className="font-medium text-green-600">
                    {results.filter(r => r.result?.success).length}
                  </div>
                </div>
                <div>
                  <div className="text-gray-500">Lỗi</div>
                  <div className="font-medium text-red-600">
                    {results.filter(r => !r.result?.success).length}
                  </div>
                </div>
                <div>
                  <div className="text-gray-500">Tỷ lệ thành công</div>
                  <div className="font-medium">
                    {results.length > 0 
                      ? Math.round((results.filter(r => r.result?.success).length / results.length) * 100)
                      : 0
                    }%
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ProcessingProgress;
