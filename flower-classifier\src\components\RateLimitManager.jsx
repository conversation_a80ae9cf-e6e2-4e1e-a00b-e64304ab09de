import React, { useState, useEffect } from 'react';
import { 
  Alert<PERSON>riangle, 
  Clock, 
  Settings, 
  RefreshCw, 
  Zap,
  TrendingDown,
  TrendingUp,
  Info
} from 'lucide-react';
import geminiService from '../services/geminiService';

const RateLimitManager = ({ isVisible, onClose, onSettingsChange }) => {
  const [rateLimitStatus, setRateLimitStatus] = useState(null);
  const [settings, setSettings] = useState({
    maxRequestsPerMinute: 15,
    baseDelay: 2000,
    maxDelay: 300000,
    respectRateLimit: true,
    autoRetry: true
  });
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Update rate limit status
  const updateStatus = () => {
    const status = geminiService.getRateLimitStatus();
    setRateLimitStatus(status);
  };

  // Auto-refresh status
  useEffect(() => {
    if (isVisible) {
      updateStatus();
      const interval = setInterval(updateStatus, 1000);
      return () => clearInterval(interval);
    }
  }, [isVisible]);

  const handleSettingChange = (key, value) => {
    const newSettings = { ...settings, [key]: value };
    setSettings(newSettings);
    
    // Update service settings
    if (key === 'maxRequestsPerMinute') {
      geminiService.maxRequestsPerMinute = value;
    }
    
    if (onSettingsChange) {
      onSettingsChange(newSettings);
    }
  };

  const handleResetRateLimit = async () => {
    setIsRefreshing(true);
    try {
      // Reset rate limit trong service
      geminiService.isRateLimited = false;
      geminiService.requestCount = 0;
      geminiService.rateLimitResetTime = 0;
      
      await new Promise(resolve => setTimeout(resolve, 1000));
      updateStatus();
    } finally {
      setIsRefreshing(false);
    }
  };

  const formatTime = (ms) => {
    if (ms <= 0) return '0s';
    const seconds = Math.ceil(ms / 1000);
    if (seconds < 60) return `${seconds}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };

  const getStatusColor = () => {
    if (!rateLimitStatus) return 'gray';
    if (rateLimitStatus.isRateLimited) return 'red';
    if (rateLimitStatus.requestCount > rateLimitStatus.maxRequests * 0.8) return 'yellow';
    return 'green';
  };

  const getStatusIcon = () => {
    const color = getStatusColor();
    switch (color) {
      case 'red':
        return <AlertTriangle className="h-5 w-5 text-red-500" />;
      case 'yellow':
        return <TrendingUp className="h-5 w-5 text-yellow-500" />;
      case 'green':
        return <TrendingDown className="h-5 w-5 text-green-500" />;
      default:
        return <Clock className="h-5 w-5 text-gray-500" />;
    }
  };

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="p-6 border-b">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-3">
              <Zap className="h-6 w-6 text-blue-500" />
              <div>
                <h2 className="text-xl font-semibold text-gray-900">
                  Quản lý Rate Limit
                </h2>
                <p className="text-sm text-gray-500">
                  Theo dõi và điều chỉnh giới hạn API
                </p>
              </div>
            </div>
            
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
            >
              ×
            </button>
          </div>
        </div>

        {/* Status */}
        <div className="p-6 border-b">
          <div className="flex items-center justify-between mb-4">
            <h3 className="font-medium text-gray-900">Trạng thái hiện tại</h3>
            <button
              onClick={updateStatus}
              disabled={isRefreshing}
              className="flex items-center px-3 py-1 text-sm text-gray-600 hover:text-gray-800 border rounded hover:bg-gray-50 disabled:opacity-50"
            >
              <RefreshCw className={`h-4 w-4 mr-1 ${isRefreshing ? 'animate-spin' : ''}`} />
              Refresh
            </button>
          </div>

          {rateLimitStatus && (
            <div className={`p-4 rounded-lg border-2 ${
              getStatusColor() === 'red' ? 'bg-red-50 border-red-200' :
              getStatusColor() === 'yellow' ? 'bg-yellow-50 border-yellow-200' :
              'bg-green-50 border-green-200'
            }`}>
              <div className="flex items-center mb-3">
                {getStatusIcon()}
                <span className="ml-2 font-medium">
                  {rateLimitStatus.isRateLimited ? 'Rate Limited' : 'Hoạt động bình thường'}
                </span>
              </div>

              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-600">Requests đã dùng:</span>
                  <div className="font-medium">
                    {rateLimitStatus.requestCount} / {rateLimitStatus.maxRequests}
                  </div>
                </div>
                
                <div>
                  <span className="text-gray-600">Thời gian reset:</span>
                  <div className="font-medium">
                    {rateLimitStatus.timeUntilReset > 0 
                      ? formatTime(rateLimitStatus.timeUntilReset)
                      : 'Ngay lập tức'
                    }
                  </div>
                </div>
              </div>

              {/* Progress bar */}
              <div className="mt-3">
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full transition-all duration-300 ${
                      getStatusColor() === 'red' ? 'bg-red-500' :
                      getStatusColor() === 'yellow' ? 'bg-yellow-500' :
                      'bg-green-500'
                    }`}
                    style={{ 
                      width: `${(rateLimitStatus.requestCount / rateLimitStatus.maxRequests) * 100}%` 
                    }}
                  ></div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Settings */}
        <div className="p-6 border-b">
          <h3 className="font-medium text-gray-900 mb-4">Cài đặt Rate Limit</h3>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Số requests tối đa mỗi phút
              </label>
              <input
                type="number"
                min="1"
                max="60"
                value={settings.maxRequestsPerMinute}
                onChange={(e) => handleSettingChange('maxRequestsPerMinute', parseInt(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm"
              />
              <p className="text-xs text-gray-500 mt-1">
                Khuyến nghị: 15 requests/phút cho Gemini API free tier
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Delay cơ bản giữa requests (ms)
              </label>
              <input
                type="number"
                min="1000"
                max="10000"
                step="500"
                value={settings.baseDelay}
                onChange={(e) => handleSettingChange('baseDelay', parseInt(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm"
              />
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="respectRateLimit"
                checked={settings.respectRateLimit}
                onChange={(e) => handleSettingChange('respectRateLimit', e.target.checked)}
                className="mr-2"
              />
              <label htmlFor="respectRateLimit" className="text-sm text-gray-700">
                Tự động tuân thủ rate limit
              </label>
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="autoRetry"
                checked={settings.autoRetry}
                onChange={(e) => handleSettingChange('autoRetry', e.target.checked)}
                className="mr-2"
              />
              <label htmlFor="autoRetry" className="text-sm text-gray-700">
                Tự động retry khi gặp rate limit
              </label>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="p-6">
          <div className="flex justify-between items-center">
            <button
              onClick={handleResetRateLimit}
              disabled={isRefreshing}
              className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
              Reset Rate Limit
            </button>

            <div className="text-sm text-gray-500">
              <div className="flex items-center">
                <Info className="h-4 w-4 mr-1" />
                Gemini API Free Tier: 15 requests/phút
              </div>
            </div>
          </div>

          {/* Tips */}
          <div className="mt-4 p-4 bg-blue-50 rounded-lg">
            <h4 className="font-medium text-blue-900 mb-2">💡 Mẹo tối ưu:</h4>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• Giảm số requests đồng thời xuống 1 để tránh rate limit</li>
              <li>• Tăng delay giữa requests lên 3-5 giây</li>
              <li>• Sử dụng cache để tránh xử lý lại file trùng lặp</li>
              <li>• Xử lý batch nhỏ (5-10 file) thay vì batch lớn</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RateLimitManager;
