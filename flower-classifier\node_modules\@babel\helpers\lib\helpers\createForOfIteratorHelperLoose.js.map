{"version": 3, "names": ["_unsupportedIterableToArray", "require", "_createForOfIteratorHelperLoose", "o", "allowArrayLike", "it", "Symbol", "iterator", "call", "next", "bind", "Array", "isArray", "unsupportedIterableToArray", "length", "i", "done", "value", "TypeError"], "sources": ["../../src/helpers/createForOfIteratorHelperLoose.ts"], "sourcesContent": ["/* @minVersion 7.9.0 */\n\nimport unsupportedIterableToArray from \"./unsupportedIterableToArray.ts\";\n\nimport type { IteratorFunction } from \"./createForOfIteratorHelper.ts\";\n\nexport default function _createForOfIteratorHelperLoose<T>(\n  o: T[] | Iterable<T> | ArrayLike<T>,\n  allowArrayLike: boolean,\n): () => IteratorResult<T, undefined> {\n  var it:\n    | IteratorFunction<T>\n    | Iterator<T>\n    | T[]\n    | IteratorResult<T, undefined>\n    | undefined =\n    (typeof Symbol !== \"undefined\" && (o as Iterable<T>)[Symbol.iterator]) ||\n    (o as any)[\"@@iterator\"];\n\n  if (it) return (it = (it as IteratorFunction<T>).call(o)).next.bind(it);\n\n  // Fallback for engines without symbol support\n  if (\n    Array.isArray(o) ||\n    // union type doesn't work with function overload, have to use \"as any\".\n    (it = unsupportedIterableToArray(o as any) as T[] | undefined) ||\n    (allowArrayLike && o && typeof (o as ArrayLike<T>).length === \"number\")\n  ) {\n    if (it) o = it;\n    var i = 0;\n    return function () {\n      // After \"Array.isArray\" check, unsupportedIterableToArray to array, and allow arraylike\n      // o is sure to be an array or arraylike, but TypeScript doesn't know that\n      if (i >= (o as T[] | ArrayLike<T>).length) {\n        // explicit missing the \"value\" (undefined) to reduce the bundle size\n        return { done: true } as IteratorReturnResult<undefined>;\n      }\n\n      return { done: false, value: (o as T[] | ArrayLike<T>)[i++] };\n    };\n  }\n\n  throw new TypeError(\n    \"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\",\n  );\n}\n"], "mappings": ";;;;;;AAEA,IAAAA,2BAAA,GAAAC,OAAA;AAIe,SAASC,+BAA+BA,CACrDC,CAAmC,EACnCC,cAAuB,EACa;EACpC,IAAIC,EAKS,GACV,OAAOC,MAAM,KAAK,WAAW,IAAKH,CAAC,CAAiBG,MAAM,CAACC,QAAQ,CAAC,IACpEJ,CAAC,CAAS,YAAY,CAAC;EAE1B,IAAIE,EAAE,EAAE,OAAO,CAACA,EAAE,GAAIA,EAAE,CAAyBG,IAAI,CAACL,CAAC,CAAC,EAAEM,IAAI,CAACC,IAAI,CAACL,EAAE,CAAC;EAGvE,IACEM,KAAK,CAACC,OAAO,CAACT,CAAC,CAAC,KAEfE,EAAE,GAAG,IAAAQ,mCAA0B,EAACV,CAAQ,CAAoB,CAAC,IAC7DC,cAAc,IAAID,CAAC,IAAI,OAAQA,CAAC,CAAkBW,MAAM,KAAK,QAAS,EACvE;IACA,IAAIT,EAAE,EAAEF,CAAC,GAAGE,EAAE;IACd,IAAIU,CAAC,GAAG,CAAC;IACT,OAAO,YAAY;MAGjB,IAAIA,CAAC,IAAKZ,CAAC,CAAwBW,MAAM,EAAE;QAEzC,OAAO;UAAEE,IAAI,EAAE;QAAK,CAAC;MACvB;MAEA,OAAO;QAAEA,IAAI,EAAE,KAAK;QAAEC,KAAK,EAAGd,CAAC,CAAwBY,CAAC,EAAE;MAAE,CAAC;IAC/D,CAAC;EACH;EAEA,MAAM,IAAIG,SAAS,CACjB,uIACF,CAAC;AACH", "ignoreList": []}