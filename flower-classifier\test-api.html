<!DOCTYPE html>
<html>
<head>
    <title>Test Gemini API</title>
</head>
<body>
    <h1>Test Gemini API</h1>
    <button onclick="testAPI()">Test API</button>
    <div id="result"></div>

    <script type="module">
        import { GoogleGenerativeAI } from 'https://esm.run/@google/generative-ai';

        const API_KEY = 'AIzaSyAVq6hfli-gbIHrHxBmaInNiTLThPVC4o4';

        window.testAPI = async function() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testing...';

            try {
                const genAI = new GoogleGenerativeAI(API_KEY);
                const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });

                const result = await model.generateContent('Hello, how are you?');
                const response = await result.response;
                const text = response.text();

                resultDiv.innerHTML = `<p style="color: green;">Success: ${text}</p>`;
                console.log('API test successful:', text);

            } catch (error) {
                resultDiv.innerHTML = `<p style="color: red;">Error: ${error.message}</p>`;
                console.error('API test failed:', error);
            }
        }
    </script>
</body>
</html>
