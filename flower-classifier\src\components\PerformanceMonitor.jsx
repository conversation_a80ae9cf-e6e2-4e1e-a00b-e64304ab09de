import React, { useState, useEffect } from 'react';
import { 
  Activity, 
  Zap, 
  Clock, 
  Target, 
  TrendingUp, 
  Database,
  RefreshCw,
  BarChart3,
  Gauge
} from 'lucide-react';
import geminiService from '../services/geminiService';

const PerformanceMonitor = ({ isVisible, onClose }) => {
  const [stats, setStats] = useState(null);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [autoRefresh, setAutoRefresh] = useState(true);

  // Update stats
  const updateStats = () => {
    const performanceStats = geminiService.getPerformanceStats();
    setStats(performanceStats);
  };

  // Auto-refresh stats
  useEffect(() => {
    if (isVisible && autoRefresh) {
      updateStats();
      const interval = setInterval(updateStats, 2000); // Update every 2 seconds
      return () => clearInterval(interval);
    }
  }, [isVisible, autoRefresh]);

  const handleRefresh = async () => {
    setIsRefreshing(true);
    await new Promise(resolve => setTimeout(resolve, 500));
    updateStats();
    setIsRefreshing(false);
  };

  const handleResetStats = () => {
    geminiService.resetStats();
    updateStats();
  };

  const handleClearCache = () => {
    geminiService.clearCache();
    updateStats();
  };

  const formatTime = (ms) => {
    if (ms < 1000) return `${Math.round(ms)}ms`;
    return `${(ms / 1000).toFixed(1)}s`;
  };

  const getPerformanceColor = (value, thresholds) => {
    if (value >= thresholds.excellent) return 'text-green-600';
    if (value >= thresholds.good) return 'text-yellow-600';
    return 'text-red-600';
  };

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="p-6 border-b">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-3">
              <Activity className="h-6 w-6 text-blue-500" />
              <div>
                <h2 className="text-xl font-semibold text-gray-900">
                  Performance Monitor
                </h2>
                <p className="text-sm text-gray-500">
                  Theo dõi hiệu suất AI và tối ưu hóa
                </p>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <label className="flex items-center text-sm">
                <input
                  type="checkbox"
                  checked={autoRefresh}
                  onChange={(e) => setAutoRefresh(e.target.checked)}
                  className="mr-2"
                />
                Auto-refresh
              </label>
              <button
                onClick={handleRefresh}
                disabled={isRefreshing}
                className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
              >
                <RefreshCw className={`h-5 w-5 ${isRefreshing ? 'animate-spin' : ''}`} />
              </button>
              <button
                onClick={onClose}
                className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
              >
                ×
              </button>
            </div>
          </div>
        </div>

        {/* Performance Stats */}
        {stats && (
          <div className="p-6">
            {/* Key Metrics */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
              <div className="bg-blue-50 p-4 rounded-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-2xl font-bold text-blue-900">
                      {formatTime(stats.averageResponseTime)}
                    </div>
                    <div className="text-sm text-blue-600">Avg Response Time</div>
                  </div>
                  <Clock className="h-8 w-8 text-blue-500" />
                </div>
                <div className={`text-xs mt-1 ${getPerformanceColor(
                  5000 - stats.averageResponseTime, 
                  { excellent: 3000, good: 2000 }
                )}`}>
                  {stats.averageResponseTime < 2000 ? 'Excellent' : 
                   stats.averageResponseTime < 3000 ? 'Good' : 'Needs Improvement'}
                </div>
              </div>

              <div className="bg-green-50 p-4 rounded-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-2xl font-bold text-green-900">
                      {((stats.successfulRequests / Math.max(stats.totalRequests, 1)) * 100).toFixed(1)}%
                    </div>
                    <div className="text-sm text-green-600">Success Rate</div>
                  </div>
                  <Target className="h-8 w-8 text-green-500" />
                </div>
                <div className="text-xs text-green-700 mt-1">
                  {stats.successfulRequests}/{stats.totalRequests} requests
                </div>
              </div>

              <div className="bg-purple-50 p-4 rounded-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-2xl font-bold text-purple-900">
                      {stats.cacheHitRate ? stats.cacheHitRate.toFixed(1) : 0}%
                    </div>
                    <div className="text-sm text-purple-600">Cache Hit Rate</div>
                  </div>
                  <Database className="h-8 w-8 text-purple-500" />
                </div>
                <div className="text-xs text-purple-700 mt-1">
                  {stats.cacheHits} hits, {stats.cacheMisses} misses
                </div>
              </div>

              <div className="bg-orange-50 p-4 rounded-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-2xl font-bold text-orange-900">
                      {stats.rateLimitStatus?.activeRequests || 0}
                    </div>
                    <div className="text-sm text-orange-600">Active Requests</div>
                  </div>
                  <Zap className="h-8 w-8 text-orange-500" />
                </div>
                <div className="text-xs text-orange-700 mt-1">
                  {stats.rateLimitStatus?.requestCount || 0}/{stats.rateLimitStatus?.maxRequests || 20} used
                </div>
              </div>
            </div>

            {/* Detailed Stats */}
            <div className="grid md:grid-cols-2 gap-6">
              {/* Performance Breakdown */}
              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="font-medium text-gray-900 mb-3 flex items-center">
                  <BarChart3 className="h-5 w-5 mr-2" />
                  Performance Breakdown
                </h3>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Total Requests:</span>
                    <span className="font-medium">{stats.totalRequests}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Successful:</span>
                    <span className="font-medium text-green-600">{stats.successfulRequests}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Failed:</span>
                    <span className="font-medium text-red-600">
                      {stats.totalRequests - stats.successfulRequests}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Cache Size:</span>
                    <span className="font-medium">{stats.cacheSize} items</span>
                  </div>
                </div>
              </div>

              {/* Rate Limit Status */}
              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="font-medium text-gray-900 mb-3 flex items-center">
                  <Gauge className="h-5 w-5 mr-2" />
                  Rate Limit Status
                </h3>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Status:</span>
                    <span className={`font-medium ${
                      stats.rateLimitStatus?.isRateLimited ? 'text-red-600' : 'text-green-600'
                    }`}>
                      {stats.rateLimitStatus?.isRateLimited ? 'Rate Limited' : 'Normal'}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Requests Used:</span>
                    <span className="font-medium">
                      {stats.rateLimitStatus?.requestCount || 0}/{stats.rateLimitStatus?.maxRequests || 20}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Time Until Reset:</span>
                    <span className="font-medium">
                      {stats.rateLimitStatus?.timeUntilReset > 0 
                        ? formatTime(stats.rateLimitStatus.timeUntilReset)
                        : 'Ready'
                      }
                    </span>
                  </div>
                  
                  {/* Progress bar */}
                  <div className="mt-2">
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full transition-all duration-300 ${
                          (stats.rateLimitStatus?.requestCount || 0) / (stats.rateLimitStatus?.maxRequests || 20) > 0.8
                            ? 'bg-red-500'
                            : (stats.rateLimitStatus?.requestCount || 0) / (stats.rateLimitStatus?.maxRequests || 20) > 0.6
                            ? 'bg-yellow-500'
                            : 'bg-green-500'
                        }`}
                        style={{ 
                          width: `${((stats.rateLimitStatus?.requestCount || 0) / (stats.rateLimitStatus?.maxRequests || 20)) * 100}%` 
                        }}
                      ></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Performance Tips */}
            <div className="mt-6 p-4 bg-blue-50 rounded-lg">
              <h3 className="font-medium text-blue-900 mb-2 flex items-center">
                <TrendingUp className="h-5 w-5 mr-2" />
                Performance Tips
              </h3>
              <div className="text-sm text-blue-800 space-y-1">
                {stats.averageResponseTime > 3000 && (
                  <div>• Response time cao - hãy giảm kích thước ảnh hoặc sử dụng cache</div>
                )}
                {stats.cacheHitRate < 50 && stats.totalRequests > 5 && (
                  <div>• Cache hit rate thấp - nhiều ảnh unique, cân nhắc tăng cache size</div>
                )}
                {(stats.rateLimitStatus?.requestCount || 0) / (stats.rateLimitStatus?.maxRequests || 20) > 0.8 && (
                  <div>• Gần đạt rate limit - hãy giảm tốc độ xử lý</div>
                )}
                {((stats.successfulRequests / Math.max(stats.totalRequests, 1)) * 100) < 90 && (
                  <div>• Success rate thấp - kiểm tra chất lượng ảnh và kết nối mạng</div>
                )}
              </div>
            </div>

            {/* Actions */}
            <div className="mt-6 flex justify-between items-center">
              <div className="flex space-x-3">
                <button
                  onClick={handleClearCache}
                  className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 text-sm"
                >
                  Clear Cache
                </button>
                <button
                  onClick={handleResetStats}
                  className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 text-sm"
                >
                  Reset Stats
                </button>
              </div>
              
              <div className="text-xs text-gray-500">
                Last updated: {new Date().toLocaleTimeString()}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PerformanceMonitor;
