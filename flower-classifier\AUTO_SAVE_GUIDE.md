# 🚀 Hướng dẫn Auto-Save - Tự động lưu hình ảnh vào folder

## 📋 Tổng quan

Tính năng Auto-Save cho phép tự động lưu hình ảnh đã phân loại vào các folder tương ứng trên máy tính của bạn:

- **C:\bó hoa** - Cho hình ảnh bó hoa
- **C:\bình hoa** - Cho hình ảnh bình hoa  
- **C:\giỏ hoa** - Cho hình ảnh giỏ hoa
- **C:\lẵng hoa** - Cho hình ảnh lẵng hoa
- **C:\vòng hoa** - Cho hình ảnh vòng hoa
- **C:\hoa cài** - Cho hình ảnh hoa cài
- **C:\hoa khác** - Cho các loại khác

## 🔧 Cách sử dụng

### Bước 1: Bật Auto-Save
1. Click vào icon **HardDrive** (💾) trên header
2. Bật toggle **Auto-Save** 
3. Ứng dụng sẽ tự động lưu file sau khi phân loại

### Bước 2: <PERSON>ấu hình Folder Paths (Tùy chọn)
1. Click vào icon **Settings** trong Auto-Save Manager
2. Chỉnh sửa đường dẫn folder cho từng danh mục
3. Đảm bảo các folder đã tồn tại trên máy tính

### Bước 3: Upload và Phân loại
1. Upload hình ảnh như bình thường
2. Sau khi phân loại xong, file sẽ tự động được lưu
3. Xem trạng thái auto-save bằng các icon:
   - ✅ **Đã lưu thành công**
   - ❌ **Lỗi khi lưu** 
   - ⏰ **Chờ xử lý**

## 🛠️ Các tính năng nâng cao

### Auto-Save Manager
- **Thống kê**: Xem số lượng file đã lưu/thất bại
- **Batch Save**: Lưu tất cả file cùng lúc
- **Retry Failed**: Thử lại các file lưu thất bại
- **Export Report**: Xuất báo cáo chi tiết

### Lưu thủ công
- **Lưu ngay**: Lưu file của danh mục cụ thể
- **Tải ZIP**: Download file theo danh mục
- **Export CSV**: Xuất thống kê

## 🔒 Bảo mật và Quyền truy cập

### File System Access API
- Ứng dụng sử dụng File System Access API của browser
- Lần đầu sử dụng, browser sẽ yêu cầu quyền truy cập folder
- Chỉ có thể truy cập folder mà bạn đã cho phép

### Fallback Options
Nếu browser không hỗ trợ File System Access API:
- File sẽ được download với prefix danh mục
- VD: `[Bó hoa] image1.jpg`

## 📁 Cấu trúc File được lưu

### File gốc
```
C:\bó hoa\
├── image1_2024-01-15T10-30-00.jpg
├── image1_2024-01-15T10-30-00_metadata.json
├── image2_2024-01-15T10-31-00.jpg
└── image2_2024-01-15T10-31-00_metadata.json
```

### File metadata
```json
{
  "originalFileName": "image1.jpg",
  "category": "Bó hoa",
  "classification": {
    "category": "Bó hoa",
    "confidence": 95,
    "description": "Bó hoa hồng đỏ với ruy băng trắng",
    "flowers": ["Hoa hồng"],
    "colors": ["Đỏ", "Trắng"]
  },
  "savedAt": "2024-01-15T10:30:00.000Z",
  "fileSize": 2048576,
  "targetPath": "C:\\bó hoa"
}
```

## ⚡ Tối ưu hóa hiệu suất

### Smart Batching
- Xử lý file theo batch để tránh overwhelm system
- Tự động delay giữa các batch
- Ưu tiên file nhỏ trước

### Caching
- Cache kết quả phân loại để tránh xử lý lại
- Tự động clear cache khi cần thiết

### Error Handling
- Retry tự động với exponential backoff
- Fallback sang download nếu auto-save thất bại
- Log chi tiết để debug

## 🚨 Xử lý sự cố

### Lỗi thường gặp

#### "Không thể truy cập folder"
**Nguyên nhân**: Browser chưa được cấp quyền
**Giải pháp**: 
1. Click "Lưu ngay" cho danh mục
2. Chọn folder đích khi browser hỏi
3. Cho phép truy cập

#### "File đã tồn tại"
**Nguyên nhân**: File cùng tên đã có trong folder
**Giải pháp**: Ứng dụng tự động thêm timestamp để tránh conflict

#### "Auto-save thất bại"
**Nguyên nhân**: Lỗi quyền truy cập hoặc folder không tồn tại
**Giải pháp**:
1. Kiểm tra folder có tồn tại không
2. Sử dụng "Retry Failed" trong Auto-Save Manager
3. Kiểm tra quyền truy cập folder

### Debug và Monitoring

#### Xem log chi tiết
1. Mở Developer Tools (F12)
2. Vào tab Console
3. Xem log auto-save

#### Export báo cáo
1. Vào Auto-Save Manager
2. Click "Export báo cáo"
3. Gửi file JSON cho support nếu cần

## 🔄 Workflow khuyến nghị

### Cho người dùng cá nhân
1. Tạo folder trước: `C:\bó hoa`, `C:\bình hoa`, `C:\giỏ hoa`
2. Bật Auto-Save
3. Upload và phân loại hình ảnh
4. Kiểm tra kết quả trong folder

### Cho doanh nghiệp
1. Cấu hình folder paths theo quy chuẩn công ty
2. Sử dụng Batch Auto-Save cho khối lượng lớn
3. Export báo cáo định kỳ
4. Backup metadata files

## 📊 Thống kê và Báo cáo

### Metrics được theo dõi
- Tổng số file xử lý
- Tỷ lệ auto-save thành công
- Thời gian xử lý trung bình
- Phân bố theo danh mục
- Lỗi và nguyên nhân

### Export formats
- **JSON**: Báo cáo chi tiết với metadata
- **CSV**: Thống kê dạng bảng
- **ZIP**: File gốc + metadata

## 🆘 Hỗ trợ

### Liên hệ
- GitHub Issues: [repository-url]/issues
- Email: <EMAIL>
- Documentation: [docs-url]

### FAQ
**Q: Có thể thay đổi folder paths không?**
A: Có, vào Auto-Save Manager > Settings để chỉnh sửa

**Q: File có bị ghi đè không?**
A: Không, ứng dụng tự động thêm timestamp để tránh conflict

**Q: Có thể auto-save vào cloud storage không?**
A: Hiện tại chỉ hỗ trợ local folders, cloud storage sẽ được thêm trong phiên bản sau

**Q: Dữ liệu có được gửi lên server không?**
A: Không, tất cả xử lý diễn ra local trên máy tính của bạn
