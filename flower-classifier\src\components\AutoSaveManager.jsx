import React, { useState, useEffect } from 'react';
import { 
  Save, 
  <PERSON>older<PERSON><PERSON>, 
  Setting<PERSON>, 
  CheckCircle, 
  XCircle, 
  RefreshCw,
  Download,
  AlertTriangle,
  Folder,
  HardDrive
} from 'lucide-react';
import fileService from '../services/fileService';

const AutoSaveManager = ({ isVisible, onClose, results }) => {
  const [autoSaveEnabled, setAutoSaveEnabled] = useState(false);
  const [folderPaths, setFolderPaths] = useState({});
  const [statistics, setStatistics] = useState(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [showSettings, setShowSettings] = useState(false);

  // Load initial data
  useEffect(() => {
    if (isVisible) {
      loadStatistics();
      setFolderPaths(fileService.folderPaths);
      setAutoSaveEnabled(fileService.autoSaveEnabled);
    }
  }, [isVisible]);

  const loadStatistics = () => {
    const stats = fileService.getAutoSaveStatistics();
    setStatistics(stats);
  };

  const handleToggleAutoSave = (enabled) => {
    setAutoSaveEnabled(enabled);
    fileService.setAutoSave(enabled);
    loadStatistics();
  };

  const handleUpdateFolderPath = (category, path) => {
    const newPaths = { ...folderPaths, [category]: path };
    setFolderPaths(newPaths);
    fileService.updateFolderPaths(newPaths);
  };

  const handleBatchAutoSave = async () => {
    setIsProcessing(true);
    try {
      const result = await fileService.batchAutoSaveAll((progress) => {
        // Update progress if needed
        console.log('Auto-save progress:', progress);
      });
      
      alert(`Auto-save hoàn thành!\nThành công: ${result.successful}\nThất bại: ${result.failed}`);
      loadStatistics();
    } catch (error) {
      alert('Có lỗi xảy ra khi auto-save: ' + error.message);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleRetryFailed = async () => {
    setIsProcessing(true);
    try {
      const result = await fileService.retryFailedAutoSaves((progress) => {
        console.log('Retry progress:', progress);
      });
      
      alert(`Retry hoàn thành!\nThành công: ${result.successful}\nThất bại: ${result.failed}`);
      loadStatistics();
    } catch (error) {
      alert('Có lỗi xảy ra khi retry: ' + error.message);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleSaveToSpecificFolder = async (category) => {
    try {
      await fileService.saveToPredefinedFolder(category);
      loadStatistics();
    } catch (error) {
      alert('Có lỗi xảy ra: ' + error.message);
    }
  };

  const handleExportReport = () => {
    fileService.exportAutoSaveReport();
  };

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="p-6 border-b">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-3">
              <HardDrive className="h-6 w-6 text-blue-500" />
              <div>
                <h2 className="text-xl font-semibold text-gray-900">
                  Quản lý Auto-Save
                </h2>
                <p className="text-sm text-gray-500">
                  Tự động lưu hình ảnh vào folder tương ứng
                </p>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setShowSettings(!showSettings)}
                className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
              >
                <Settings className="h-5 w-5" />
              </button>
              <button
                onClick={onClose}
                className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
              >
                ×
              </button>
            </div>
          </div>

          {/* Auto-save toggle */}
          <div className="mt-4 flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div>
              <h3 className="font-medium text-gray-900">Auto-Save</h3>
              <p className="text-sm text-gray-600">
                Tự động lưu file vào folder sau khi phân loại
              </p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={autoSaveEnabled}
                onChange={(e) => handleToggleAutoSave(e.target.checked)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>
        </div>

        {/* Statistics */}
        {statistics && (
          <div className="p-6 border-b">
            <h3 className="font-medium text-gray-900 mb-4">Thống kê Auto-Save</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
              <div className="bg-blue-50 p-4 rounded-lg">
                <div className="text-2xl font-bold text-blue-900">{statistics.totalFiles}</div>
                <div className="text-sm text-blue-600">Tổng số file</div>
              </div>
              <div className="bg-green-50 p-4 rounded-lg">
                <div className="text-2xl font-bold text-green-900">{statistics.autoSavedFiles}</div>
                <div className="text-sm text-green-600">Đã lưu</div>
              </div>
              <div className="bg-red-50 p-4 rounded-lg">
                <div className="text-2xl font-bold text-red-900">{statistics.failedFiles}</div>
                <div className="text-sm text-red-600">Thất bại</div>
              </div>
              <div className="bg-yellow-50 p-4 rounded-lg">
                <div className="text-2xl font-bold text-yellow-900">{statistics.pendingFiles}</div>
                <div className="text-sm text-yellow-600">Chờ xử lý</div>
              </div>
            </div>
            
            <div className="text-center">
              <div className="text-lg font-semibold text-gray-900">
                Tỷ lệ thành công: {statistics.overallSaveRate.toFixed(1)}%
              </div>
            </div>
          </div>
        )}

        {/* Folder Configuration */}
        <div className="p-6 border-b">
          <div className="flex justify-between items-center mb-4">
            <h3 className="font-medium text-gray-900">Cấu hình Folder</h3>
            {showSettings && (
              <button
                onClick={() => setShowSettings(false)}
                className="text-sm text-gray-500 hover:text-gray-700"
              >
                Ẩn cài đặt
              </button>
            )}
          </div>

          <div className="space-y-3">
            {Object.entries(folderPaths).map(([category, path]) => (
              <div key={category} className="flex items-center space-x-3">
                <Folder className="h-5 w-5 text-gray-400" />
                <div className="flex-1">
                  <div className="font-medium text-gray-900">{category}</div>
                  {showSettings ? (
                    <input
                      type="text"
                      value={path}
                      onChange={(e) => handleUpdateFolderPath(category, e.target.value)}
                      className="w-full mt-1 px-3 py-1 border border-gray-300 rounded text-sm"
                      placeholder="VD: C:\bó hoa"
                    />
                  ) : (
                    <div className="text-sm text-gray-600">{path}</div>
                  )}
                </div>
                
                {statistics?.categoryStats[category] && (
                  <div className="text-right">
                    <div className="flex items-center space-x-2">
                      {statistics.categoryStats[category].saved > 0 && (
                        <CheckCircle className="h-4 w-4 text-green-500" />
                      )}
                      {statistics.categoryStats[category].failed > 0 && (
                        <XCircle className="h-4 w-4 text-red-500" />
                      )}
                      <span className="text-sm text-gray-600">
                        {statistics.categoryStats[category].saved}/{statistics.categoryStats[category].total}
                      </span>
                    </div>
                  </div>
                )}
                
                <button
                  onClick={() => handleSaveToSpecificFolder(category)}
                  disabled={!statistics?.categoryStats[category]?.total}
                  className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Lưu ngay
                </button>
              </div>
            ))}
          </div>
        </div>

        {/* Actions */}
        <div className="p-6">
          <div className="flex flex-wrap gap-3">
            <button
              onClick={handleBatchAutoSave}
              disabled={isProcessing || !statistics?.pendingFiles}
              className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Save className="h-4 w-4 mr-2" />
              {isProcessing ? 'Đang xử lý...' : 'Auto-Save tất cả'}
            </button>
            
            {statistics?.failedFiles > 0 && (
              <button
                onClick={handleRetryFailed}
                disabled={isProcessing}
                className="flex items-center px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 disabled:opacity-50"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Retry thất bại ({statistics.failedFiles})
              </button>
            )}
            
            <button
              onClick={handleExportReport}
              className="flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
            >
              <Download className="h-4 w-4 mr-2" />
              Export báo cáo
            </button>
            
            <button
              onClick={loadStatistics}
              className="flex items-center px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </button>
          </div>
          
          {/* Help text */}
          <div className="mt-4 p-4 bg-blue-50 rounded-lg">
            <div className="flex items-start space-x-2">
              <AlertTriangle className="h-5 w-5 text-blue-500 mt-0.5" />
              <div className="text-sm text-blue-800">
                <p className="font-medium mb-1">Hướng dẫn sử dụng:</p>
                <ul className="list-disc list-inside space-y-1">
                  <li>Bật Auto-Save để tự động lưu file sau khi phân loại</li>
                  <li>Cấu hình đường dẫn folder cho từng danh mục</li>
                  <li>Sử dụng "Lưu ngay" để lưu file của danh mục cụ thể</li>
                  <li>Browser sẽ yêu cầu quyền truy cập folder khi lần đầu lưu</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AutoSaveManager;
