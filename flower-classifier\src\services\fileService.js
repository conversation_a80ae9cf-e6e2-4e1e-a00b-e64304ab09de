/**
 * Service để xử lý các thao tác với file system
 * Lưu ý: Trong môi trường web browser, chúng ta không thể trực tiếp tạo thư mục
 * hoặc di chuyển file trên hệ thống. Thay vào đó, chúng ta sẽ:
 * 1. Tạo cấu trúc thư mục ảo
 * 2. Cho phép người dùng download file theo từng danh mục
 * 3. Hoặc sử dụng File System Access API (nếu browser hỗ trợ)
 */

class FileService {
  constructor() {
    this.supportsFSA = 'showDirectoryPicker' in window;
    this.organizedFiles = new Map(); // Map để lưu trữ file theo danh mục

    // Predefined folder paths
    this.folderPaths = {
      'Bó hoa': 'C:\\bó hoa',
      'Bình hoa': 'C:\\bình hoa',
      'Giỏ hoa': 'C:\\giỏ hoa',
      'Lẵng hoa': 'C:\\lẵng hoa',
      'Vòng hoa': 'C:\\vòng hoa',
      'Hoa cài': 'C:\\hoa cài',
      'Khác': 'C:\\hoa khác'
    };

    // Track auto-save status
    this.autoSaveEnabled = true; // Bật auto-save mặc định
    this.autoSaveResults = new Map();
  }

  /**
   * Khởi tạo cấu trúc thư mục cho các danh mục hoa
   */
  initializeFolderStructure() {
    const categories = ['Bó hoa', 'Giỏ hoa', 'Bình hoa', 'Khác', 'Lỗi phân loại'];
    
    categories.forEach(category => {
      if (!this.organizedFiles.has(category)) {
        this.organizedFiles.set(category, []);
      }
    });
  }

  /**
   * Cập nhật folder paths
   * @param {Object} newPaths - Object chứa paths mới
   */
  updateFolderPaths(newPaths) {
    this.folderPaths = { ...this.folderPaths, ...newPaths };
  }

  /**
   * Bật/tắt auto-save
   * @param {boolean} enabled - Trạng thái auto-save
   */
  setAutoSave(enabled) {
    this.autoSaveEnabled = enabled;
  }

  /**
   * Thêm file vào danh mục tương ứng và tự động lưu nếu được bật
   * @param {string} category - Danh mục
   * @param {File} file - File cần thêm
   * @param {Object} classificationResult - Kết quả phân loại
   */
  async addFileToCategory(category, file, classificationResult) {
    this.initializeFolderStructure();

    const fileInfo = {
      file: file,
      fileName: file.name,
      classification: classificationResult,
      addedAt: new Date(),
      autoSaved: false,
      targetPath: this.folderPaths[category] || this.folderPaths['Khác']
    };

    const categoryFiles = this.organizedFiles.get(category) || [];
    categoryFiles.push(fileInfo);
    this.organizedFiles.set(category, categoryFiles);

    // Auto-save nếu được bật
    if (this.autoSaveEnabled) {
      try {
        await this.autoSaveFile(category, fileInfo);
      } catch (error) {
        console.error('Auto-save failed:', error);
        fileInfo.autoSaveError = error.message;
      }
    }
  }

  /**
   * Tự động lưu file vào folder tương ứng
   * @param {string} category - Danh mục
   * @param {Object} fileInfo - Thông tin file
   */
  async autoSaveFile(category, fileInfo) {
    try {
      // Luôn download file với prefix category để user có thể tự sắp xếp
      this.downloadFileWithCategoryPrefix(fileInfo.file, category);
      fileInfo.autoSaved = true;
      fileInfo.saveMethod = 'download';

      this.autoSaveResults.set(fileInfo.fileName, {
        success: true,
        category,
        path: `Downloads/${category}_${fileInfo.fileName}`,
        timestamp: new Date(),
        method: 'download'
      });

      console.log(`✅ Auto-saved: ${fileInfo.fileName} -> ${category}`);

    } catch (error) {
      console.error('Failed to auto-save file:', error);
      fileInfo.autoSaveError = error.message;

      this.autoSaveResults.set(fileInfo.fileName, {
        success: false,
        category,
        error: error.message,
        timestamp: new Date()
      });
    }
  }

  /**
   * Lưu file vào path cụ thể sử dụng File System Access API
   * @param {File} file - File cần lưu
   * @param {string} category - Danh mục
   */
  async saveFileToSpecificPath(file, category) {
    try {
      // Request directory access
      const dirHandle = await window.showDirectoryPicker({
        id: 'flower-classifier-' + category,
        startIn: 'desktop'
      });

      // Tạo file trong directory
      const fileName = this.generateUniqueFileName(file.name);
      const fileHandle = await dirHandle.getFileHandle(fileName, { create: true });
      const writable = await fileHandle.createWritable();

      await writable.write(file);
      await writable.close();

      // Tạo file metadata
      const metadataFileName = fileName.replace(/\.[^/.]+$/, '') + '_metadata.json';
      const metadataHandle = await dirHandle.getFileHandle(metadataFileName, { create: true });
      const metadataWritable = await metadataHandle.createWritable();

      const metadata = {
        originalFileName: file.name,
        category: category,
        classification: this.getFileClassification(file.name),
        savedAt: new Date().toISOString(),
        fileSize: file.size,
        targetPath: this.folderPaths[category]
      };

      await metadataWritable.write(JSON.stringify(metadata, null, 2));
      await metadataWritable.close();

      return true;
    } catch (error) {
      if (error.name === 'AbortError') {
        console.log('User cancelled directory selection');
      }
      throw error;
    }
  }

  /**
   * Download file với prefix category
   * @param {File} file - File cần download
   * @param {string} category - Danh mục
   */
  downloadFileWithCategoryPrefix(file, category) {
    const url = URL.createObjectURL(file);
    const a = document.createElement('a');
    a.href = url;
    a.download = `[${category}] ${file.name}`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }

  /**
   * Tạo tên file unique để tránh conflict
   * @param {string} originalName - Tên file gốc
   */
  generateUniqueFileName(originalName) {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const extension = originalName.split('.').pop();
    const nameWithoutExt = originalName.replace(/\.[^/.]+$/, '');
    return `${nameWithoutExt}_${timestamp}.${extension}`;
  }

  /**
   * Lấy thông tin classification của file
   * @param {string} fileName - Tên file
   */
  getFileClassification(fileName) {
    for (const [category, files] of this.organizedFiles.entries()) {
      const fileInfo = files.find(f => f.fileName === fileName);
      if (fileInfo) {
        return fileInfo.classification;
      }
    }
    return null;
  }

  /**
   * Lấy danh sách file theo danh mục
   * @param {string} category - Danh mục
   * @returns {Array} - Danh sách file
   */
  getFilesByCategory(category) {
    return this.organizedFiles.get(category) || [];
  }

  /**
   * Lấy tất cả danh mục và số lượng file
   * @returns {Object} - Object chứa thông tin các danh mục
   */
  getCategorySummary() {
    const summary = {};
    
    for (const [category, files] of this.organizedFiles.entries()) {
      summary[category] = {
        count: files.length,
        files: files.map(item => ({
          fileName: item.fileName,
          confidence: item.classification?.data?.confidence || 0,
          addedAt: item.addedAt
        }))
      };
    }
    
    return summary;
  }

  /**
   * Download file theo danh mục dưới dạng ZIP (sử dụng JSZip)
   * @param {string} category - Danh mục cần download
   */
  async downloadCategoryAsZip(category) {
    try {
      // Import JSZip dynamically
      const JSZip = (await import('jszip')).default;
      const zip = new JSZip();
      
      const files = this.getFilesByCategory(category);
      const folder = zip.folder(category);
      
      // Thêm file vào ZIP
      for (const item of files) {
        const arrayBuffer = await item.file.arrayBuffer();
        folder.file(item.fileName, arrayBuffer);
        
        // Thêm file thông tin phân loại
        const info = {
          fileName: item.fileName,
          category: category,
          classification: item.classification,
          processedAt: item.addedAt
        };
        folder.file(`${item.fileName}_info.json`, JSON.stringify(info, null, 2));
      }
      
      // Tạo và download ZIP
      const content = await zip.generateAsync({ type: 'blob' });
      const url = URL.createObjectURL(content);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${category}_${new Date().toISOString().split('T')[0]}.zip`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
    } catch (error) {
      console.error('Lỗi khi tạo ZIP:', error);
      // Fallback: download từng file riêng lẻ
      this.downloadCategoryFiles(category);
    }
  }

  /**
   * Download từng file riêng lẻ trong danh mục
   * @param {string} category - Danh mục cần download
   */
  downloadCategoryFiles(category) {
    const files = this.getFilesByCategory(category);
    
    files.forEach((item, index) => {
      setTimeout(() => {
        const url = URL.createObjectURL(item.file);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${category}_${item.fileName}`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      }, index * 500); // Delay để tránh browser block multiple downloads
    });
  }

  /**
   * Batch auto-save tất cả files đã phân loại
   * @param {Function} onProgress - Callback báo cáo tiến độ
   */
  async batchAutoSaveAll(onProgress = null) {
    const allCategories = Array.from(this.organizedFiles.keys());
    let totalFiles = 0;
    let processedFiles = 0;

    // Đếm tổng số files
    for (const category of allCategories) {
      totalFiles += this.getFilesByCategory(category).length;
    }

    const results = {
      successful: 0,
      failed: 0,
      details: []
    };

    for (const category of allCategories) {
      const files = this.getFilesByCategory(category);

      for (const fileInfo of files) {
        if (fileInfo.autoSaved) {
          processedFiles++;
          continue; // Skip đã save rồi
        }

        if (onProgress) {
          onProgress({
            current: processedFiles + 1,
            total: totalFiles,
            category: category,
            fileName: fileInfo.fileName,
            status: 'processing'
          });
        }

        try {
          await this.autoSaveFile(category, fileInfo);
          results.successful++;
          results.details.push({
            fileName: fileInfo.fileName,
            category: category,
            status: 'success',
            path: this.folderPaths[category]
          });
        } catch (error) {
          results.failed++;
          results.details.push({
            fileName: fileInfo.fileName,
            category: category,
            status: 'error',
            error: error.message
          });
        }

        processedFiles++;

        if (onProgress) {
          onProgress({
            current: processedFiles,
            total: totalFiles,
            category: category,
            fileName: fileInfo.fileName,
            status: 'completed'
          });
        }

        // Delay nhỏ để tránh overwhelm system
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    return results;
  }

  /**
   * Lưu vào folder được định sẵn (C:\bó hoa, C:\bình hoa, C:\giỏ hoa)
   * @param {string} category - Danh mục
   */
  async saveToPredefinedFolder(category) {
    const targetPath = this.folderPaths[category];
    if (!targetPath) {
      throw new Error(`Không tìm thấy path cho danh mục: ${category}`);
    }

    if (!this.supportsFSA) {
      // Fallback: download với tên folder
      const files = this.getFilesByCategory(category);
      files.forEach((item, index) => {
        setTimeout(() => {
          this.downloadFileWithCategoryPrefix(item.file, category);
        }, index * 200);
      });
      return;
    }

    try {
      // Hiển thị dialog để user chọn folder (sẽ suggest predefined path)
      const dirHandle = await window.showDirectoryPicker({
        id: 'flower-classifier-' + category,
        startIn: 'desktop'
      });

      const files = this.getFilesByCategory(category);
      const results = [];

      for (const item of files) {
        try {
          const fileName = this.generateUniqueFileName(item.fileName);
          const fileHandle = await dirHandle.getFileHandle(fileName, { create: true });
          const writable = await fileHandle.createWritable();

          await writable.write(item.file);
          await writable.close();

          // Tạo file metadata
          const metadataFileName = fileName.replace(/\.[^/.]+$/, '') + '_metadata.json';
          const metadataHandle = await dirHandle.getFileHandle(metadataFileName, { create: true });
          const metadataWritable = await metadataHandle.createWritable();

          const metadata = {
            originalFileName: item.fileName,
            category: category,
            classification: item.classification,
            savedAt: new Date().toISOString(),
            confidence: item.classification?.data?.confidence || 0,
            targetPath: targetPath
          };

          await metadataWritable.write(JSON.stringify(metadata, null, 2));
          await metadataWritable.close();

          results.push({ fileName: item.fileName, status: 'success' });

          // Mark as auto-saved
          item.autoSaved = true;
          item.saveMethod = 'predefined_folder';

        } catch (error) {
          console.error(`Failed to save ${item.fileName}:`, error);
          results.push({ fileName: item.fileName, status: 'error', error: error.message });
        }
      }

      const successCount = results.filter(r => r.status === 'success').length;
      alert(`Đã lưu ${successCount}/${files.length} file vào folder ${category}`);

      return results;

    } catch (error) {
      console.error('Lỗi khi lưu vào predefined folder:', error);
      if (error.name === 'AbortError') {
        console.log('Người dùng hủy chọn thư mục');
      } else {
        alert('Có lỗi xảy ra khi lưu file. Sử dụng chức năng download thay thế.');
        this.downloadCategoryAsZip(category);
      }
    }
  }

  /**
   * Sử dụng File System Access API để lưu file (nếu browser hỗ trợ)
   * @param {string} category - Danh mục
   */
  async saveToFileSystem(category) {
    // Sử dụng predefined folder nếu có
    if (this.folderPaths[category]) {
      return this.saveToPredefinedFolder(category);
    }

    if (!this.supportsFSA) {
      alert('Trình duyệt không hỗ trợ File System Access API. Sử dụng chức năng download thay thế.');
      return this.downloadCategoryAsZip(category);
    }

    try {
      // Chọn thư mục đích
      const dirHandle = await window.showDirectoryPicker();

      // Tạo thư mục con cho danh mục
      const categoryDirHandle = await dirHandle.getDirectoryHandle(category, { create: true });

      const files = this.getFilesByCategory(category);

      for (const item of files) {
        // Tạo file
        const fileHandle = await categoryDirHandle.getFileHandle(item.fileName, { create: true });
        const writable = await fileHandle.createWritable();

        // Ghi dữ liệu
        await writable.write(item.file);
        await writable.close();

        // Tạo file thông tin
        const infoFileName = `${item.fileName}_info.json`;
        const infoFileHandle = await categoryDirHandle.getFileHandle(infoFileName, { create: true });
        const infoWritable = await infoFileHandle.createWritable();

        const info = {
          fileName: item.fileName,
          category: category,
          classification: item.classification,
          processedAt: item.addedAt
        };

        await infoWritable.write(JSON.stringify(info, null, 2));
        await infoWritable.close();
      }

      alert(`Đã lưu ${files.length} file vào thư mục ${category}`);

    } catch (error) {
      console.error('Lỗi khi lưu file:', error);
      if (error.name === 'AbortError') {
        console.log('Người dùng hủy chọn thư mục');
      } else {
        alert('Có lỗi xảy ra khi lưu file. Sử dụng chức năng download thay thế.');
        this.downloadCategoryAsZip(category);
      }
    }
  }

  /**
   * Xóa tất cả file đã phân loại
   */
  clearAllFiles() {
    this.organizedFiles.clear();
    this.initializeFolderStructure();
  }

  /**
   * Xóa file khỏi danh mục
   * @param {string} category - Danh mục
   * @param {string} fileName - Tên file cần xóa
   */
  removeFileFromCategory(category, fileName) {
    const files = this.organizedFiles.get(category) || [];
    const updatedFiles = files.filter(item => item.fileName !== fileName);
    this.organizedFiles.set(category, updatedFiles);
  }

  /**
   * Export thống kê dưới dạng CSV
   * @returns {string} - Dữ liệu CSV
   */
  exportStatisticsAsCSV() {
    const headers = ['Danh mục', 'Tên file', 'Độ tin cậy', 'Mô tả', 'Loại hoa', 'Màu sắc', 'Thời gian xử lý'];
    const rows = [headers];
    
    for (const [category, files] of this.organizedFiles.entries()) {
      files.forEach(item => {
        const classification = item.classification?.data || {};
        rows.push([
          category,
          item.fileName,
          classification.confidence || 0,
          classification.description || '',
          (classification.flowers || []).join('; '),
          (classification.colors || []).join('; '),
          item.addedAt?.toLocaleString() || ''
        ]);
      });
    }
    
    return rows.map(row => row.map(cell => `"${cell}"`).join(',')).join('\n');
  }

  /**
   * Lấy thống kê auto-save
   */
  getAutoSaveStatistics() {
    let totalFiles = 0;
    let autoSavedFiles = 0;
    let failedFiles = 0;
    const categoryStats = {};

    for (const [category, files] of this.organizedFiles.entries()) {
      const categoryTotal = files.length;
      const categorySaved = files.filter(f => f.autoSaved).length;
      const categoryFailed = files.filter(f => f.autoSaveError).length;

      totalFiles += categoryTotal;
      autoSavedFiles += categorySaved;
      failedFiles += categoryFailed;

      categoryStats[category] = {
        total: categoryTotal,
        saved: categorySaved,
        failed: categoryFailed,
        saveRate: categoryTotal > 0 ? (categorySaved / categoryTotal) * 100 : 0,
        targetPath: this.folderPaths[category] || 'Không xác định'
      };
    }

    return {
      totalFiles,
      autoSavedFiles,
      failedFiles,
      pendingFiles: totalFiles - autoSavedFiles - failedFiles,
      overallSaveRate: totalFiles > 0 ? (autoSavedFiles / totalFiles) * 100 : 0,
      categoryStats,
      autoSaveEnabled: this.autoSaveEnabled,
      folderPaths: this.folderPaths
    };
  }

  /**
   * Retry failed auto-saves
   */
  async retryFailedAutoSaves(onProgress = null) {
    const failedFiles = [];

    for (const [category, files] of this.organizedFiles.entries()) {
      const failed = files.filter(f => f.autoSaveError && !f.autoSaved);
      failedFiles.push(...failed.map(f => ({ ...f, category })));
    }

    if (failedFiles.length === 0) {
      return { message: 'Không có file nào cần retry', successful: 0, failed: 0 };
    }

    let successful = 0;
    let failed = 0;

    for (let i = 0; i < failedFiles.length; i++) {
      const fileInfo = failedFiles[i];

      if (onProgress) {
        onProgress({
          current: i + 1,
          total: failedFiles.length,
          fileName: fileInfo.fileName,
          category: fileInfo.category,
          status: 'retrying'
        });
      }

      try {
        // Clear previous error
        delete fileInfo.autoSaveError;

        await this.autoSaveFile(fileInfo.category, fileInfo);
        successful++;

        if (onProgress) {
          onProgress({
            current: i + 1,
            total: failedFiles.length,
            fileName: fileInfo.fileName,
            category: fileInfo.category,
            status: 'success'
          });
        }
      } catch (error) {
        fileInfo.autoSaveError = error.message;
        failed++;

        if (onProgress) {
          onProgress({
            current: i + 1,
            total: failedFiles.length,
            fileName: fileInfo.fileName,
            category: fileInfo.category,
            status: 'failed',
            error: error.message
          });
        }
      }

      // Delay between retries
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    return { successful, failed, total: failedFiles.length };
  }

  /**
   * Kiểm tra và tạo folder structure nếu cần
   */
  async validateFolderStructure() {
    const validation = {
      accessible: {},
      errors: {},
      recommendations: []
    };

    for (const [category, path] of Object.entries(this.folderPaths)) {
      try {
        // Trong browser environment, chúng ta không thể trực tiếp check folder existence
        // Nhưng có thể validate path format
        if (!path || path.trim() === '') {
          validation.errors[category] = 'Path không được để trống';
          continue;
        }

        // Basic path validation
        if (!path.match(/^[A-Za-z]:\\/)) {
          validation.errors[category] = 'Path phải bắt đầu với drive letter (VD: C:\\)';
          continue;
        }

        validation.accessible[category] = path;
      } catch (error) {
        validation.errors[category] = error.message;
      }
    }

    // Recommendations
    if (Object.keys(validation.errors).length > 0) {
      validation.recommendations.push('Kiểm tra và sửa các path không hợp lệ');
    }

    if (!this.autoSaveEnabled) {
      validation.recommendations.push('Bật auto-save để tự động lưu file vào folder');
    }

    return validation;
  }

  /**
   * Export auto-save report
   */
  exportAutoSaveReport() {
    const stats = this.getAutoSaveStatistics();
    const report = {
      generatedAt: new Date().toISOString(),
      summary: {
        totalFiles: stats.totalFiles,
        autoSavedFiles: stats.autoSavedFiles,
        failedFiles: stats.failedFiles,
        pendingFiles: stats.pendingFiles,
        overallSaveRate: stats.overallSaveRate.toFixed(2) + '%'
      },
      folderConfiguration: stats.folderPaths,
      categoryDetails: stats.categoryStats,
      autoSaveResults: Array.from(this.autoSaveResults.entries()).map(([fileName, result]) => ({
        fileName,
        ...result
      }))
    };

    const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `auto_save_report_${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }

  /**
   * Download thống kê dưới dạng CSV
   */
  downloadStatistics() {
    const csvContent = this.exportStatisticsAsCSV();
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `flower_classification_statistics_${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }
}

export default new FileService();
