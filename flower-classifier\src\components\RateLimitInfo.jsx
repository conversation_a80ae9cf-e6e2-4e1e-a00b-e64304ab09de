import React, { useState, useEffect } from 'react';
import { Clock, AlertTriangle, CheckCircle, Info } from 'lucide-react';
import geminiService from '../services/geminiService';

const RateLimitInfo = ({ isProcessing }) => {
  const [rateLimitStatus, setRateLimitStatus] = useState({
    isRateLimited: false,
    requestCount: 0,
    maxRequests: 10,
    timeUntilReset: 0
  });

  useEffect(() => {
    const updateStatus = () => {
      const status = geminiService.getRateLimitStatus();
      setRateLimitStatus(status);
    };

    // Cập nhật ngay lập tức
    updateStatus();

    // Cập nhật mỗi giây khi đang xử lý
    let interval;
    if (isProcessing) {
      interval = setInterval(updateStatus, 1000);
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [isProcessing]);

  const getStatusColor = () => {
    if (rateLimitStatus.isRateLimited) {
      return 'text-red-600 bg-red-50 border-red-200';
    } else if (rateLimitStatus.requestCount >= rateLimitStatus.maxRequests * 0.8) {
      return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    } else {
      return 'text-green-600 bg-green-50 border-green-200';
    }
  };

  const getStatusIcon = () => {
    if (rateLimitStatus.isRateLimited) {
      return <AlertTriangle className="h-4 w-4" />;
    } else if (rateLimitStatus.requestCount >= rateLimitStatus.maxRequests * 0.8) {
      return <Clock className="h-4 w-4" />;
    } else {
      return <CheckCircle className="h-4 w-4" />;
    }
  };

  const getStatusText = () => {
    if (rateLimitStatus.isRateLimited) {
      return 'Đang chờ rate limit';
    } else if (rateLimitStatus.requestCount >= rateLimitStatus.maxRequests * 0.8) {
      return 'Gần đạt giới hạn';
    } else {
      return 'Hoạt động bình thường';
    }
  };

  const formatTimeUntilReset = (ms) => {
    const seconds = Math.ceil(ms / 1000);
    if (seconds <= 0) return '0s';
    
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    
    if (minutes > 0) {
      return `${minutes}m ${remainingSeconds}s`;
    } else {
      return `${remainingSeconds}s`;
    }
  };

  return (
    <div className={`border rounded-lg p-4 mb-4 ${getStatusColor()}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          {getStatusIcon()}
          <div>
            <h4 className="font-medium">API Rate Limit</h4>
            <p className="text-sm opacity-90">{getStatusText()}</p>
          </div>
        </div>
        
        <div className="text-right">
          <div className="text-sm font-medium">
            {rateLimitStatus.requestCount}/{rateLimitStatus.maxRequests} requests
          </div>
          {rateLimitStatus.isRateLimited && (
            <div className="text-xs opacity-75">
              Reset trong: {formatTimeUntilReset(rateLimitStatus.timeUntilReset)}
            </div>
          )}
        </div>
      </div>
      
      {/* Progress bar */}
      <div className="mt-3">
        <div className="w-full bg-white bg-opacity-50 rounded-full h-2">
          <div 
            className="h-2 rounded-full transition-all duration-300"
            style={{
              width: `${(rateLimitStatus.requestCount / rateLimitStatus.maxRequests) * 100}%`,
              backgroundColor: rateLimitStatus.isRateLimited ? '#dc2626' : 
                             rateLimitStatus.requestCount >= rateLimitStatus.maxRequests * 0.8 ? '#d97706' : '#16a34a'
            }}
          />
        </div>
      </div>
      
      {/* Tips */}
      <div className="mt-3 flex items-start space-x-2 text-xs opacity-75">
        <Info className="h-3 w-3 mt-0.5 flex-shrink-0" />
        <div>
          <p>
            <strong>Mẹo:</strong> Upload ít file hơn (5-8 ảnh/lần) để tránh rate limit. 
            Hệ thống sẽ tự động retry và chờ khi cần thiết.
          </p>
        </div>
      </div>
    </div>
  );
};

export default RateLimitInfo;
