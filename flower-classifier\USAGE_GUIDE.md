# 📋 Hướng dẫn sử dụng chi tiết

## 🎯 Mục đích
Công cụ này giúp bạn tự động phân loại hình ảnh hoa thành các danh mục khác nhau, tiết kiệm thời gian sắp xếp và tổ chức file.

## 🚀 Bắt đầu nhanh

### Bước 1: <PERSON><PERSON><PERSON> bị hình ảnh
- Chuẩn bị các file hình ảnh hoa cần phân loại
- Đả<PERSON> bảo file có định dạng: JPG, PNG, GIF, hoặc WebP
- Kích thước file không quá 10MB

### Bước 2: Upload hình ảnh
1. Mở ứng dụng tại `http://localhost:5173/`
2. Kéo thả file vào vùng upload hoặc click "chọn file"
3. Xem preview các hình ảnh đã chọn
4. Click "Bắt đầu phân loại"

### Bước 3: <PERSON>õ<PERSON> tiế<PERSON> độ
- Xem thanh progress bar
- <PERSON> d<PERSON> file đang được xử lý
- Xem kết quả real-time

### Bước 4: Xem và tải kết quả
- Xem kết quả phân loại theo danh mục
- Click vào danh mục để xem chi tiết
- Tải xuống file theo danh mục

## 📊 Hiểu kết quả phân loại

### Thông tin hiển thị
- **Danh mục**: Loại cách sắp xếp hoa (Bó hoa, Giỏ hoa, Bình hoa, Khác)
- **Độ tin cậy**: Mức độ chắc chắn của AI (0-100%)
- **Mô tả**: Mô tả chi tiết về cách sắp xếp
- **Loại hoa**: Các loại hoa được nhận diện
- **Màu sắc**: Màu sắc chủ đạo

### Ví dụ kết quả
```json
{
  "category": "Bó hoa",
  "confidence": 92,
  "description": "Bó hoa hồng đỏ được buộc bằng ruy băng trắng",
  "flowers": ["Hoa hồng", "Hoa baby"],
  "colors": ["Đỏ", "Trắng", "Xanh lá"]
}
```

## 💾 Quản lý file

### Tải xuống theo danh mục
1. Chọn danh mục muốn tải
2. Click "Tải ZIP" để tải tất cả file trong danh mục
3. File ZIP sẽ chứa:
   - Hình ảnh gốc
   - File JSON với thông tin phân loại

### Lưu trực tiếp vào thư mục
1. Click "Lưu vào thư mục"
2. Chọn thư mục đích trên máy tính
3. File sẽ được tự động tổ chức theo danh mục

### Export thống kê
- Click "Xuất thống kê" để tải file CSV
- File CSV chứa tất cả thông tin phân loại
- Có thể mở bằng Excel hoặc Google Sheets

## ⚙️ Tùy chỉnh và cấu hình

### Thay đổi API Key
1. Mở file `src/services/geminiService.js`
2. Thay đổi giá trị `API_KEY`
3. Restart ứng dụng

### Tùy chỉnh danh mục
1. Mở file `src/services/geminiService.js`
2. Chỉnh sửa prompt trong hàm `classifyFlowerImage`
3. Thêm/sửa danh mục trong phần "Các danh mục chính"

### Thay đổi giới hạn file
1. Mở file `src/services/geminiService.js`
2. Sửa giá trị `maxSize` trong hàm `isValidImageFile`

## 🔧 Xử lý sự cố

### Lỗi "API Key không hợp lệ"
- Kiểm tra API key có đúng không
- Đảm bảo API key có quyền truy cập Gemini API
- Kiểm tra quota còn lại

### Lỗi "File quá lớn"
- Giảm kích thước file xuống dưới 10MB
- Sử dụng công cụ nén ảnh online

### Lỗi "Định dạng không hỗ trợ"
- Chuyển đổi file sang JPG, PNG, GIF, hoặc WebP
- Kiểm tra file có bị hỏng không

### Kết quả không chính xác
- Đảm bảo hình ảnh rõ nét và có ánh sáng tốt
- Hình ảnh nên tập trung vào cách sắp xếp hoa
- Tránh hình ảnh có nhiều đối tượng khác

## 💡 Mẹo sử dụng hiệu quả

### Chuẩn bị hình ảnh tốt
- Chụp ảnh rõ nét, đủ sáng
- Tập trung vào cách sắp xếp hoa
- Tránh che khuất bởi đối tượng khác

### Xử lý hàng loạt
- Upload tối đa 20-30 file mỗi lần
- Chờ batch hiện tại hoàn thành trước khi upload batch mới
- Kiểm tra kết quả trước khi xử lý batch tiếp theo

### Tổ chức file
- Đặt tên file có ý nghĩa trước khi upload
- Sử dụng chức năng export thống kê để theo dõi
- Backup file gốc trước khi xử lý

## 📈 Hiệu suất và giới hạn

### Thời gian xử lý
- 2-5 giây/hình ảnh (tùy kích thước)
- Xử lý tuần tự để tránh rate limiting
- Delay 500ms giữa các request

### Giới hạn API
- 60 requests/phút (Gemini API free tier)
- 1500 requests/ngày
- Tự động retry khi gặp rate limit

### Độ chính xác
- 85-95% với hình ảnh chất lượng tốt
- Thấp hơn với hình ảnh mờ hoặc phức tạp
- Tốt nhất với hình ảnh tập trung vào hoa

## 🔒 Bảo mật và quyền riêng tư

### Xử lý dữ liệu
- Hình ảnh được gửi đến Google Gemini API
- Không lưu trữ hình ảnh trên server
- Kết quả chỉ lưu trong browser

### API Key
- Không chia sẻ API key với người khác
- Sử dụng environment variables trong production
- Theo dõi usage để tránh vượt quota

## 📞 Hỗ trợ kỹ thuật

### Báo lỗi
1. Mô tả chi tiết lỗi gặp phải
2. Cung cấp screenshot nếu có thể
3. Ghi rõ browser và version đang sử dụng

### Yêu cầu tính năng
1. Mô tả tính năng mong muốn
2. Giải thích use case cụ thể
3. Đánh giá mức độ ưu tiên

### Liên hệ
- GitHub Issues: [repository-url]/issues
- Email: [<EMAIL>]
- Discord: [discord-server-link]
