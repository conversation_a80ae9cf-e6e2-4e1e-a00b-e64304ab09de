import React, { useState } from 'react';
import { Download, <PERSON>older<PERSON>pen, Eye, BarChart3, FileText, Trash2, CheckCircle, XCircle, Clock } from 'lucide-react';
import fileService from '../services/fileService';

const ResultsDisplay = ({ results, onClear }) => {
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [showDetails, setShowDetails] = useState({});

  if (!results || results.length === 0) {
    return null;
  }

  // Tổ chức kết quả theo danh mục
  const organizeByCategory = () => {
    const categories = {};
    
    results.forEach(item => {
      if (item.result?.success) {
        const category = item.result.data?.category || 'Khác';
        if (!categories[category]) {
          categories[category] = [];
        }
        categories[category].push(item);
      } else {
        if (!categories['Lỗi phân loại']) {
          categories['Lỗi phân loại'] = [];
        }
        categories['Lỗi phân loại'].push(item);
      }
    });
    
    return categories;
  };

  const categorizedResults = organizeByCategory();
  const categoryNames = Object.keys(categorizedResults);

  const toggleDetails = (fileName) => {
    setShowDetails(prev => ({
      ...prev,
      [fileName]: !prev[fileName]
    }));
  };

  const handleDownloadCategory = async (category) => {
    try {
      await fileService.downloadCategoryAsZip(category);
    } catch (error) {
      console.error('Lỗi khi download:', error);
      alert('Có lỗi xảy ra khi tải xuống. Vui lòng thử lại.');
    }
  };

  const handleSaveToFileSystem = async (category) => {
    try {
      await fileService.saveToPredefinedFolder(category);
    } catch (error) {
      console.error('Lỗi khi lưu file:', error);
    }
  };

  const getAutoSaveStatus = (fileName) => {
    // Tìm file trong organized files để lấy auto-save status
    for (const [category, files] of fileService.organizedFiles.entries()) {
      const fileInfo = files.find(f => f.fileName === fileName);
      if (fileInfo) {
        if (fileInfo.autoSaved) {
          return { status: 'saved', method: fileInfo.saveMethod };
        } else if (fileInfo.autoSaveError) {
          return { status: 'error', error: fileInfo.autoSaveError };
        } else {
          return { status: 'pending' };
        }
      }
    }
    return { status: 'unknown' };
  };

  const getAutoSaveIcon = (status) => {
    switch (status.status) {
      case 'saved':
        return <CheckCircle className="h-4 w-4 text-green-500" title="Đã auto-save" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" title={`Lỗi auto-save: ${status.error}`} />;
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500" title="Chờ auto-save" />;
      default:
        return null;
    }
  };

  const handleDownloadStatistics = () => {
    fileService.downloadStatistics();
  };

  const filteredResults = selectedCategory === 'all' 
    ? results 
    : categorizedResults[selectedCategory] || [];

  return (
    <div className="w-full max-w-6xl mx-auto p-6">
      <div className="bg-white rounded-lg shadow-sm border">
        {/* Header */}
        <div className="p-6 border-b">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-semibold text-gray-900">
              Kết quả phân loại hoa
            </h2>
            <div className="flex space-x-2">
              <button
                onClick={handleDownloadStatistics}
                className="flex items-center px-4 py-2 text-sm text-gray-600 hover:text-gray-800 border rounded-lg hover:bg-gray-50"
              >
                <FileText className="h-4 w-4 mr-2" />
                Xuất thống kê
              </button>
              <button
                onClick={onClear}
                className="flex items-center px-4 py-2 text-sm text-red-600 hover:text-red-800 border border-red-200 rounded-lg hover:bg-red-50"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Xóa tất cả
              </button>
            </div>
          </div>

          {/* Bộ lọc danh mục */}
          <div className="mt-4 flex flex-wrap gap-2">
            <button
              onClick={() => setSelectedCategory('all')}
              className={`px-4 py-2 rounded-lg text-sm font-medium ${
                selectedCategory === 'all'
                  ? 'bg-blue-100 text-blue-800'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
            >
              Tất cả ({results.length})
            </button>
            {categoryNames.map(category => (
              <button
                key={category}
                onClick={() => setSelectedCategory(category)}
                className={`px-4 py-2 rounded-lg text-sm font-medium ${
                  selectedCategory === category
                    ? 'bg-blue-100 text-blue-800'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
              >
                {category} ({categorizedResults[category].length})
              </button>
            ))}
          </div>
        </div>

        {/* Danh mục actions */}
        {selectedCategory !== 'all' && (
          <div className="p-4 bg-gray-50 border-b">
            <div className="flex justify-between items-center">
              <h3 className="font-medium text-gray-900">
                Danh mục: {selectedCategory}
              </h3>
              <div className="flex space-x-2">
                <button
                  onClick={() => handleDownloadCategory(selectedCategory)}
                  className="flex items-center px-3 py-2 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Tải ZIP
                </button>
                <button
                  onClick={() => handleSaveToFileSystem(selectedCategory)}
                  className="flex items-center px-3 py-2 text-sm bg-green-600 text-white rounded-lg hover:bg-green-700"
                >
                  <FolderOpen className="h-4 w-4 mr-2" />
                  Lưu vào folder
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Danh sách kết quả */}
        <div className="p-6">
          {filteredResults.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              Không có kết quả nào trong danh mục này.
            </div>
          ) : (
            <div className="grid gap-4">
              {filteredResults.map((item, index) => (
                <div key={index} className="border rounded-lg overflow-hidden">
                  <div className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3">
                          <h4 className="font-medium text-gray-900">
                            {item.fileName}
                          </h4>
                          
                          {item.result?.success ? (
                            <span className="px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full">
                              {item.result.data?.category || 'Không xác định'}
                            </span>
                          ) : (
                            <span className="px-2 py-1 bg-red-100 text-red-800 text-xs font-medium rounded-full">
                              Lỗi
                            </span>
                          )}

                          {/* Auto-save status */}
                          {getAutoSaveIcon(getAutoSaveStatus(item.fileName))}
                        </div>

                        {item.result?.success && (
                          <div className="mt-2 text-sm text-gray-600">
                            <div>
                              <strong>Độ tin cậy:</strong> {item.result.data?.confidence || 0}%
                            </div>
                            {item.result.data?.description && (
                              <div className="mt-1">
                                <strong>Mô tả:</strong> {item.result.data.description}
                              </div>
                            )}
                          </div>
                        )}

                        {!item.result?.success && (
                          <div className="mt-2 text-sm text-red-600">
                            <strong>Lỗi:</strong> {item.result?.error || 'Lỗi không xác định'}
                          </div>
                        )}
                      </div>

                      <button
                        onClick={() => toggleDetails(item.fileName)}
                        className="flex items-center px-3 py-2 text-sm text-gray-600 hover:text-gray-800 border rounded-lg hover:bg-gray-50"
                      >
                        <Eye className="h-4 w-4 mr-2" />
                        {showDetails[item.fileName] ? 'Ẩn' : 'Xem'} chi tiết
                      </button>
                    </div>

                    {/* Chi tiết mở rộng */}
                    {showDetails[item.fileName] && item.result?.success && (
                      <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                        <div className="grid md:grid-cols-2 gap-4">
                          {item.result.data?.flowers && item.result.data.flowers.length > 0 && (
                            <div>
                              <h5 className="font-medium text-gray-900 mb-2">Loại hoa:</h5>
                              <div className="flex flex-wrap gap-1">
                                {item.result.data.flowers.map((flower, idx) => (
                                  <span
                                    key={idx}
                                    className="px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded-full"
                                  >
                                    {flower}
                                  </span>
                                ))}
                              </div>
                            </div>
                          )}

                          {item.result.data?.colors && item.result.data.colors.length > 0 && (
                            <div>
                              <h5 className="font-medium text-gray-900 mb-2">Màu sắc:</h5>
                              <div className="flex flex-wrap gap-1">
                                {item.result.data.colors.map((color, idx) => (
                                  <span
                                    key={idx}
                                    className="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full"
                                  >
                                    {color}
                                  </span>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>

                        {item.result.rawResponse && (
                          <div className="mt-4">
                            <h5 className="font-medium text-gray-900 mb-2">Raw Response:</h5>
                            <pre className="text-xs bg-white p-2 rounded border overflow-x-auto">
                              {JSON.stringify(item.result.data, null, 2)}
                            </pre>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ResultsDisplay;
