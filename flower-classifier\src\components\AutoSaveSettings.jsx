import React, { useState, useEffect } from 'react';
import { FolderOpen, Download, Settings, CheckCircle, XCircle, AlertCircle } from 'lucide-react';
import fileService from '../services/fileService';

const AutoSaveSettings = () => {
  const [autoSaveEnabled, setAutoSaveEnabled] = useState(true);
  const [folderStatus, setFolderStatus] = useState({});
  const [isCreatingFolders, setIsCreatingFolders] = useState(false);

  useEffect(() => {
    // Kiểm tra trạng thái auto-save
    setAutoSaveEnabled(fileService.autoSaveEnabled);
    checkFolderStatus();
  }, []);

  const checkFolderStatus = async () => {
    // Kiểm tra xem các thư mục có tồn tại không (simulation)
    const categories = ['Bó hoa', 'Bình hoa', 'Giỏ hoa', 'Hoa cài', 'Khác'];
    const status = {};
    
    categories.forEach(category => {
      // Trong thực tế, bạn có thể kiểm tra thư mục thật
      status[category] = {
        exists: false, // Mặc định là chưa tồn tại
        path: fileService.folderPaths[category]
      };
    });
    
    setFolderStatus(status);
  };

  const toggleAutoSave = () => {
    const newState = !autoSaveEnabled;
    setAutoSaveEnabled(newState);
    fileService.setAutoSave(newState);
  };

  const createFoldersOnDesktop = async () => {
    setIsCreatingFolders(true);
    
    try {
      if (!('showDirectoryPicker' in window)) {
        alert('Trình duyệt không hỗ trợ File System Access API. Các file sẽ được download thay thế.');
        return;
      }

      // Yêu cầu người dùng chọn thư mục gốc (Desktop)
      const rootDirHandle = await window.showDirectoryPicker({
        id: 'flower-classifier-root',
        startIn: 'desktop'
      });

      const categories = ['Bó hoa', 'Bình hoa', 'Giỏ hoa', 'Hoa cài', 'Khác'];
      const newStatus = { ...folderStatus };

      for (const category of categories) {
        try {
          // Tạo thư mục cho từng danh mục
          await rootDirHandle.getDirectoryHandle(category, { create: true });
          newStatus[category] = {
            exists: true,
            path: `Desktop/${category}`
          };
        } catch (error) {
          console.error(`Lỗi tạo thư mục ${category}:`, error);
          newStatus[category] = {
            exists: false,
            path: fileService.folderPaths[category],
            error: error.message
          };
        }
      }

      setFolderStatus(newStatus);
      alert('Đã tạo thành công các thư mục phân loại hoa trên Desktop!');
      
    } catch (error) {
      console.error('Lỗi tạo thư mục:', error);
      if (error.name === 'AbortError') {
        alert('Đã hủy tạo thư mục.');
      } else {
        alert('Có lỗi xảy ra khi tạo thư mục. Vui lòng thử lại.');
      }
    } finally {
      setIsCreatingFolders(false);
    }
  };

  const downloadAllCategories = async () => {
    try {
      const categories = Object.keys(fileService.organizedFiles);
      
      for (const category of categories) {
        await fileService.downloadCategoryAsZip(category);
        // Delay nhỏ giữa các download
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    } catch (error) {
      console.error('Lỗi download:', error);
      alert('Có lỗi xảy ra khi download. Vui lòng thử lại.');
    }
  };

  const getFolderStatusIcon = (status) => {
    if (status?.exists) {
      return <CheckCircle className="h-4 w-4 text-green-500" />;
    } else if (status?.error) {
      return <XCircle className="h-4 w-4 text-red-500" />;
    } else {
      return <AlertCircle className="h-4 w-4 text-yellow-500" />;
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border p-6 mb-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900 flex items-center">
          <Settings className="h-5 w-5 mr-2" />
          Cài đặt lưu file
        </h3>
      </div>

      {/* Auto-save toggle */}
      <div className="flex items-center justify-between mb-6 p-4 bg-gray-50 rounded-lg">
        <div>
          <h4 className="font-medium text-gray-900">Tự động lưu file</h4>
          <p className="text-sm text-gray-600">
            Tự động lưu các ảnh đã phân loại vào thư mục tương ứng
          </p>
        </div>
        <button
          onClick={toggleAutoSave}
          className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
            autoSaveEnabled ? 'bg-blue-600' : 'bg-gray-200'
          }`}
        >
          <span
            className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
              autoSaveEnabled ? 'translate-x-6' : 'translate-x-1'
            }`}
          />
        </button>
      </div>

      {/* Folder creation */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-3">
          <h4 className="font-medium text-gray-900">Thư mục phân loại</h4>
          <button
            onClick={createFoldersOnDesktop}
            disabled={isCreatingFolders}
            className="flex items-center px-3 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 disabled:opacity-50"
          >
            <FolderOpen className="h-4 w-4 mr-2" />
            {isCreatingFolders ? 'Đang tạo...' : 'Tạo thư mục'}
          </button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {Object.entries(folderStatus).map(([category, status]) => (
            <div key={category} className="flex items-center justify-between p-3 border rounded-lg">
              <div className="flex items-center">
                {getFolderStatusIcon(status)}
                <span className="ml-2 text-sm font-medium">{category}</span>
              </div>
              <span className="text-xs text-gray-500 truncate max-w-32">
                {status?.path || 'Chưa tạo'}
              </span>
            </div>
          ))}
        </div>
      </div>

      {/* Download options */}
      <div className="border-t pt-4">
        <h4 className="font-medium text-gray-900 mb-3">Tùy chọn download</h4>
        <div className="flex flex-wrap gap-3">
          <button
            onClick={downloadAllCategories}
            className="flex items-center px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700"
          >
            <Download className="h-4 w-4 mr-2" />
            Download tất cả (ZIP)
          </button>
          
          <button
            onClick={() => fileService.downloadStatistics()}
            className="flex items-center px-4 py-2 bg-gray-600 text-white text-sm font-medium rounded-md hover:bg-gray-700"
          >
            <Download className="h-4 w-4 mr-2" />
            Download thống kê
          </button>
        </div>
      </div>

      {/* Status info */}
      <div className="mt-4 p-3 bg-blue-50 rounded-lg">
        <p className="text-sm text-blue-800">
          <strong>Lưu ý:</strong> {autoSaveEnabled 
            ? 'Auto-save đang bật. Các file sẽ được tự động lưu vào thư mục tương ứng.' 
            : 'Auto-save đang tắt. Bạn cần download file thủ công.'
          }
        </p>
      </div>
    </div>
  );
};

export default AutoSaveSettings;
