import { useState, useCallback, useEffect } from 'react';
import geminiService from '../services/geminiService';
import fileService from '../services/fileService';

export const useAppState = () => {
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(null);
  const [results, setResults] = useState([]);
  const [error, setError] = useState(null);
  const [isInitialized, setIsInitialized] = useState(false);

  // Initialize app
  useEffect(() => {
    const initializeApp = async () => {
      try {
        // Initialize file service
        fileService.clearAllFiles();
        
        // Check if API key is configured
        const hasApiKey = geminiService.isConfigured();
        if (!hasApiKey) {
          setError('API key chưa được cấu hình. Vui lòng kiểm tra file cấu hình.');
        }
        
        setIsInitialized(true);
      } catch (err) {
        console.error('Failed to initialize app:', err);
        setError('<PERSON>hông thể khởi động ứng dụng. Vui lòng tải lại trang.');
        setIsInitialized(true);
      }
    };

    initializeApp();
  }, []);

  const handleFilesSelected = useCallback(async (files) => {
    if (!files || files.length === 0) return;

    setIsProcessing(true);
    setProgress(null);
    setResults([]);
    setError(null);
    
    try {
      // Clear previous data
      fileService.clearAllFiles();
      
      // Process files in batches
      const batchResults = await geminiService.classifyMultipleImages(
        files,
        (progressInfo) => {
          setProgress(progressInfo);
        }
      );
      
      // Organize results by category với auto-save
      for (const item of batchResults) {
        if (item.result.success) {
          const category = item.result.data?.category || 'Khác';
          await fileService.addFileToCategory(category, item.file, item.result);
        } else {
          await fileService.addFileToCategory('Lỗi phân loại', item.file, item.result);
        }
      }
      
      setResults(batchResults);
      setProgress(null);
      
      // Show success notification
      const successCount = batchResults.filter(r => r.result.success).length;
      const totalCount = batchResults.length;
      
      if (successCount === totalCount) {
        console.log(`✅ Đã phân loại thành công ${successCount}/${totalCount} hình ảnh`);
      } else {
        console.warn(`⚠️ Phân loại thành công ${successCount}/${totalCount} hình ảnh`);
      }
      
    } catch (err) {
      console.error('Error processing files:', err);
      setError('Có lỗi xảy ra khi xử lý hình ảnh. Vui lòng thử lại.');
    } finally {
      setIsProcessing(false);
    }
  }, []);

  const handleClearResults = useCallback(() => {
    setResults([]);
    setProgress(null);
    setError(null);
    fileService.clearAllFiles();
  }, []);

  const handleRetry = useCallback(() => {
    setError(null);
    setProgress(null);
  }, []);

  const getStatistics = useCallback(() => {
    if (results.length === 0) return null;

    const stats = {
      total: results.length,
      successful: results.filter(r => r.result.success).length,
      failed: results.filter(r => !r.result.success).length,
      categories: {}
    };

    stats.successRate = Math.round((stats.successful / stats.total) * 100);

    // Count by categories
    results.forEach(item => {
      if (item.result.success) {
        const category = item.result.data?.category || 'Khác';
        stats.categories[category] = (stats.categories[category] || 0) + 1;
      }
    });

    return stats;
  }, [results]);

  const exportResults = useCallback(async (format = 'csv') => {
    try {
      if (format === 'csv') {
        fileService.downloadStatistics();
      } else if (format === 'json') {
        const data = {
          timestamp: new Date().toISOString(),
          statistics: getStatistics(),
          results: results.map(item => ({
            fileName: item.fileName,
            success: item.result.success,
            category: item.result.success ? item.result.data?.category : null,
            confidence: item.result.success ? item.result.data?.confidence : null,
            error: item.result.success ? null : item.result.error
          }))
        };
        
        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `flower_classification_${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      }
    } catch (err) {
      console.error('Export failed:', err);
      setError('Không thể xuất dữ liệu. Vui lòng thử lại.');
    }
  }, [results, getStatistics]);

  return {
    // State
    isProcessing,
    progress,
    results,
    error,
    isInitialized,
    
    // Actions
    handleFilesSelected,
    handleClearResults,
    handleRetry,
    exportResults,
    
    // Computed
    statistics: getStatistics(),
    
    // Utils
    hasResults: results.length > 0,
    hasError: !!error,
    canProcess: !isProcessing && isInitialized && !error
  };
};
