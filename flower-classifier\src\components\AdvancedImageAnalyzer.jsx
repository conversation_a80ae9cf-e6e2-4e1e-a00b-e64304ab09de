import React, { useState, useCallback, useEffect } from 'react';
import { 
  Camera, 
  Zap, 
  BarChart3, 
  Settings, 
  CheckCircle, 
  AlertTriangle, 
  XCircle,
  Eye,
  Sliders,
  TrendingUp
} from 'lucide-react';
import geminiService from '../services/geminiService';

const AdvancedImageAnalyzer = ({ files, onAnalysisComplete, onSettingsChange }) => {
  const [analysisResults, setAnalysisResults] = useState([]);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [settings, setSettings] = useState({
    autoEnhance: true,
    qualityThreshold: 70,
    maxConcurrent: 3,
    retryAttempts: 2,
    prioritizeBySize: true,
    useSmartBatching: true
  });
  const [showAdvancedSettings, setShowAdvancedSettings] = useState(false);

  // Phân tích chất lượng hình <PERSON>nh
  const analyzeImageQuality = useCallback(async (files) => {
    if (!files || files.length === 0) return;

    setIsAnalyzing(true);
    const results = [];

    for (const file of files) {
      try {
        const qualityAnalysis = await geminiService.analyzeImageQuality(file);
        const thumbnail = await geminiService.createThumbnail(file);
        
        results.push({
          file,
          fileName: file.name,
          fileSize: file.size,
          qualityAnalysis,
          thumbnail: URL.createObjectURL(thumbnail),
          status: qualityAnalysis.qualityScore >= settings.qualityThreshold ? 'good' : 'warning'
        });
      } catch (error) {
        results.push({
          file,
          fileName: file.name,
          fileSize: file.size,
          error: error.message,
          status: 'error'
        });
      }
    }

    setAnalysisResults(results);
    setIsAnalyzing(false);
    
    if (onAnalysisComplete) {
      onAnalysisComplete(results);
    }
  }, [settings.qualityThreshold, onAnalysisComplete]);

  // Auto-enhance images
  const enhanceImages = useCallback(async () => {
    const enhancedResults = await Promise.all(
      analysisResults.map(async (result) => {
        if (result.qualityAnalysis && result.status === 'warning') {
          try {
            const enhancedFile = await geminiService.enhanceImageQuality(
              result.file, 
              result.qualityAnalysis
            );
            const newQualityAnalysis = await geminiService.analyzeImageQuality(enhancedFile);
            
            return {
              ...result,
              enhancedFile,
              originalQuality: result.qualityAnalysis,
              qualityAnalysis: newQualityAnalysis,
              status: newQualityAnalysis.qualityScore >= settings.qualityThreshold ? 'enhanced' : 'warning',
              isEnhanced: true
            };
          } catch (error) {
            return { ...result, enhanceError: error.message };
          }
        }
        return result;
      })
    );

    setAnalysisResults(enhancedResults);
  }, [analysisResults, settings.qualityThreshold]);

  // Update settings
  const updateSettings = useCallback((newSettings) => {
    setSettings(prev => ({ ...prev, ...newSettings }));
    if (onSettingsChange) {
      onSettingsChange({ ...settings, ...newSettings });
    }
  }, [settings, onSettingsChange]);

  // Auto-analyze when files change
  useEffect(() => {
    if (files && files.length > 0) {
      analyzeImageQuality(files);
    }
  }, [files, analyzeImageQuality]);

  const getStatusIcon = (status) => {
    switch (status) {
      case 'good': return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'enhanced': return <Zap className="h-5 w-5 text-blue-500" />;
      case 'warning': return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
      case 'error': return <XCircle className="h-5 w-5 text-red-500" />;
      default: return <Eye className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'good': return 'bg-green-50 border-green-200';
      case 'enhanced': return 'bg-blue-50 border-blue-200';
      case 'warning': return 'bg-yellow-50 border-yellow-200';
      case 'error': return 'bg-red-50 border-red-200';
      default: return 'bg-gray-50 border-gray-200';
    }
  };

  if (!files || files.length === 0) {
    return null;
  }

  return (
    <div className="w-full max-w-6xl mx-auto p-6">
      <div className="bg-white rounded-lg shadow-sm border">
        {/* Header */}
        <div className="p-6 border-b">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-3">
              <Camera className="h-6 w-6 text-blue-500" />
              <div>
                <h2 className="text-xl font-semibold text-gray-900">
                  Phân tích chất lượng hình ảnh
                </h2>
                <p className="text-sm text-gray-500">
                  AI-powered quality analysis và auto-enhancement
                </p>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setShowAdvancedSettings(!showAdvancedSettings)}
                className="flex items-center px-3 py-2 text-sm text-gray-600 hover:text-gray-800 border rounded-lg hover:bg-gray-50"
              >
                <Settings className="h-4 w-4 mr-2" />
                Cài đặt
              </button>
              
              {analysisResults.some(r => r.status === 'warning') && (
                <button
                  onClick={enhanceImages}
                  className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                >
                  <Zap className="h-4 w-4 mr-2" />
                  Auto-enhance
                </button>
              )}
            </div>
          </div>

          {/* Advanced Settings */}
          {showAdvancedSettings && (
            <div className="mt-4 p-4 bg-gray-50 rounded-lg">
              <h3 className="font-medium text-gray-900 mb-3">Cài đặt nâng cao</h3>
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Ngưỡng chất lượng
                  </label>
                  <input
                    type="range"
                    min="50"
                    max="90"
                    value={settings.qualityThreshold}
                    onChange={(e) => updateSettings({ qualityThreshold: parseInt(e.target.value) })}
                    className="w-full"
                  />
                  <span className="text-xs text-gray-500">{settings.qualityThreshold}%</span>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Xử lý song song
                  </label>
                  <select
                    value={settings.maxConcurrent}
                    onChange={(e) => updateSettings({ maxConcurrent: parseInt(e.target.value) })}
                    className="w-full px-3 py-1 border border-gray-300 rounded text-sm"
                  >
                    <option value={1}>1 (Chậm nhưng ổn định)</option>
                    <option value={2}>2 (Cân bằng)</option>
                    <option value={3}>3 (Nhanh)</option>
                    <option value={4}>4 (Rất nhanh)</option>
                  </select>
                </div>
                
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="autoEnhance"
                    checked={settings.autoEnhance}
                    onChange={(e) => updateSettings({ autoEnhance: e.target.checked })}
                    className="mr-2"
                  />
                  <label htmlFor="autoEnhance" className="text-sm text-gray-700">
                    Tự động cải thiện chất lượng
                  </label>
                </div>
                
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="smartBatching"
                    checked={settings.useSmartBatching}
                    onChange={(e) => updateSettings({ useSmartBatching: e.target.checked })}
                    className="mr-2"
                  />
                  <label htmlFor="smartBatching" className="text-sm text-gray-700">
                    Smart batching
                  </label>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Analysis Results */}
        <div className="p-6">
          {isAnalyzing ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-600">Đang phân tích chất lượng hình ảnh...</p>
            </div>
          ) : analysisResults.length > 0 ? (
            <>
              {/* Summary Stats */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                <div className="bg-green-50 p-4 rounded-lg">
                  <div className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                    <span className="text-sm font-medium text-green-800">Chất lượng tốt</span>
                  </div>
                  <div className="text-2xl font-bold text-green-900">
                    {analysisResults.filter(r => r.status === 'good').length}
                  </div>
                </div>
                
                <div className="bg-yellow-50 p-4 rounded-lg">
                  <div className="flex items-center">
                    <AlertTriangle className="h-5 w-5 text-yellow-500 mr-2" />
                    <span className="text-sm font-medium text-yellow-800">Cần cải thiện</span>
                  </div>
                  <div className="text-2xl font-bold text-yellow-900">
                    {analysisResults.filter(r => r.status === 'warning').length}
                  </div>
                </div>
                
                <div className="bg-blue-50 p-4 rounded-lg">
                  <div className="flex items-center">
                    <Zap className="h-5 w-5 text-blue-500 mr-2" />
                    <span className="text-sm font-medium text-blue-800">Đã cải thiện</span>
                  </div>
                  <div className="text-2xl font-bold text-blue-900">
                    {analysisResults.filter(r => r.status === 'enhanced').length}
                  </div>
                </div>
                
                <div className="bg-gray-50 p-4 rounded-lg">
                  <div className="flex items-center">
                    <BarChart3 className="h-5 w-5 text-gray-500 mr-2" />
                    <span className="text-sm font-medium text-gray-800">Điểm TB</span>
                  </div>
                  <div className="text-2xl font-bold text-gray-900">
                    {Math.round(
                      analysisResults
                        .filter(r => r.qualityAnalysis)
                        .reduce((sum, r) => sum + r.qualityAnalysis.qualityScore, 0) /
                      analysisResults.filter(r => r.qualityAnalysis).length || 0
                    )}
                  </div>
                </div>
              </div>

              {/* Individual Results */}
              <div className="space-y-4">
                {analysisResults.map((result, index) => (
                  <div
                    key={index}
                    className={`border rounded-lg p-4 ${getStatusColor(result.status)}`}
                  >
                    <div className="flex items-start space-x-4">
                      {/* Thumbnail */}
                      {result.thumbnail && (
                        <img
                          src={result.thumbnail}
                          alt={result.fileName}
                          className="w-16 h-16 object-cover rounded"
                        />
                      )}
                      
                      {/* Info */}
                      <div className="flex-1">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            {getStatusIcon(result.status)}
                            <h4 className="font-medium text-gray-900">{result.fileName}</h4>
                          </div>
                          
                          {result.qualityAnalysis && (
                            <div className="text-right">
                              <div className="text-lg font-bold text-gray-900">
                                {result.qualityAnalysis.qualityScore}/100
                              </div>
                              <div className="text-xs text-gray-500">Quality Score</div>
                            </div>
                          )}
                        </div>
                        
                        {result.qualityAnalysis && (
                          <div className="mt-2 grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                            <div>
                              <span className="text-gray-500">Độ phân giải:</span>
                              <div className="font-medium">
                                {result.qualityAnalysis.width} × {result.qualityAnalysis.height}
                              </div>
                            </div>
                            <div>
                              <span className="text-gray-500">Độ sáng:</span>
                              <div className="font-medium">{result.qualityAnalysis.brightness}</div>
                            </div>
                            <div>
                              <span className="text-gray-500">Độ tương phản:</span>
                              <div className="font-medium">{result.qualityAnalysis.contrast}</div>
                            </div>
                            <div>
                              <span className="text-gray-500">Kích thước:</span>
                              <div className="font-medium">
                                {(result.fileSize / 1024 / 1024).toFixed(1)} MB
                              </div>
                            </div>
                          </div>
                        )}
                        
                        {result.qualityAnalysis?.recommendations && (
                          <div className="mt-2">
                            <div className="text-xs text-gray-600">
                              <strong>Khuyến nghị:</strong> {result.qualityAnalysis.recommendations.join(', ')}
                            </div>
                          </div>
                        )}
                        
                        {result.isEnhanced && (
                          <div className="mt-2 flex items-center text-sm text-blue-600">
                            <TrendingUp className="h-4 w-4 mr-1" />
                            Đã cải thiện từ {result.originalQuality.qualityScore} → {result.qualityAnalysis.qualityScore}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </>
          ) : (
            <div className="text-center py-8 text-gray-500">
              Chưa có kết quả phân tích
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AdvancedImageAnalyzer;
