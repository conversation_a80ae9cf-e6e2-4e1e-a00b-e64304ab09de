{"version": 3, "names": ["_remapping", "data", "require", "mergeSourceMap", "inputMap", "map", "sourceFileName", "source", "replace", "found", "result", "remapping", "rootless", "s", "ctx", "sourceRoot", "Object", "assign"], "sources": ["../../../src/transformation/file/merge-map.ts"], "sourcesContent": ["type SourceMap = any;\nimport remapping from \"@ampproject/remapping\";\n\nexport default function mergeSourceMap(\n  inputMap: SourceMap,\n  map: SourceMap,\n  sourceFileName: string,\n): SourceMap {\n  // On win32 machines, the sourceFileName uses backslash paths like\n  // `C:\\foo\\bar.js`. But sourcemaps are always posix paths, so we need to\n  // normalize to regular slashes before we can merge (else we won't find the\n  // source associated with our input map).\n  // This mirrors code done while generating the output map at\n  // https://github.com/babel/babel/blob/5c2fcadc9ae34fd20dd72b1111d5cf50476d700d/packages/babel-generator/src/source-map.ts#L102\n  const source = sourceFileName.replace(/\\\\/g, \"/\");\n\n  // Prevent an infinite recursion if one of the input map's sources has the\n  // same resolved path as the input map. In the case, it would keep find the\n  // input map, then get it's sources which will include a path like the input\n  // map, on and on.\n  let found = false;\n  const result = remapping(rootless(map), (s, ctx) => {\n    if (s === source && !found) {\n      found = true;\n      // We empty the source location, which will prevent the sourcemap from\n      // becoming relative to the input's location. Eg, if we're transforming a\n      // file 'foo/bar.js', and it is a transformation of a `baz.js` file in the\n      // same directory, the expected output is just `baz.js`. Without this step,\n      // it would become `foo/baz.js`.\n      ctx.source = \"\";\n\n      return rootless(inputMap);\n    }\n\n    return null;\n  });\n\n  if (typeof inputMap.sourceRoot === \"string\") {\n    result.sourceRoot = inputMap.sourceRoot;\n  }\n\n  // remapping returns a SourceMap class type, but this breaks code downstream in\n  // @babel/traverse and @babel/types that relies on data being plain objects.\n  // When it encounters the sourcemap type it outputs a \"don't know how to turn\n  // this value into a node\" error. As a result, we are converting the merged\n  // sourcemap to a plain js object.\n  return { ...result };\n}\n\nfunction rootless(map: SourceMap): SourceMap {\n  return {\n    ...map,\n\n    // This is a bit hack. Remapping will create absolute sources in our\n    // sourcemap, but we want to maintain sources relative to the sourceRoot.\n    // We'll re-add the sourceRoot after remapping.\n    sourceRoot: null,\n  };\n}\n"], "mappings": ";;;;;;AACA,SAAAA,WAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,UAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEe,SAASE,cAAcA,CACpCC,QAAmB,EACnBC,GAAc,EACdC,cAAsB,EACX;EAOX,MAAMC,MAAM,GAAGD,cAAc,CAACE,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;EAMjD,IAAIC,KAAK,GAAG,KAAK;EACjB,MAAMC,MAAM,GAAGC,WAAQA,CAAC,CAACC,QAAQ,CAACP,GAAG,CAAC,EAAE,CAACQ,CAAC,EAAEC,GAAG,KAAK;IAClD,IAAID,CAAC,KAAKN,MAAM,IAAI,CAACE,KAAK,EAAE;MAC1BA,KAAK,GAAG,IAAI;MAMZK,GAAG,CAACP,MAAM,GAAG,EAAE;MAEf,OAAOK,QAAQ,CAACR,QAAQ,CAAC;IAC3B;IAEA,OAAO,IAAI;EACb,CAAC,CAAC;EAEF,IAAI,OAAOA,QAAQ,CAACW,UAAU,KAAK,QAAQ,EAAE;IAC3CL,MAAM,CAACK,UAAU,GAAGX,QAAQ,CAACW,UAAU;EACzC;EAOA,OAAAC,MAAA,CAAAC,MAAA,KAAYP,MAAM;AACpB;AAEA,SAASE,QAAQA,CAACP,GAAc,EAAa;EAC3C,OAAAW,MAAA,CAAAC,MAAA,KACKZ,GAAG;IAKNU,UAAU,EAAE;EAAI;AAEpB;AAAC", "ignoreList": []}