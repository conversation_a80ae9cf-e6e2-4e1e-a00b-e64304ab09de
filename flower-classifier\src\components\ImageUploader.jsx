import React, { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { Upload, Image, X, AlertCircle } from 'lucide-react';

const ImageUploader = ({ onFilesSelected, isProcessing, disabled = false }) => {
  const [selectedFiles, setSelectedFiles] = useState([]);
  const [errors, setErrors] = useState([]);

  const onDrop = useCallback((acceptedFiles, rejectedFiles) => {
    // Xử lý file bị từ chối
    if (rejectedFiles.length > 0) {
      const newErrors = rejectedFiles.map(({ file, errors }) => ({
        fileName: file.name,
        errors: errors.map(e => e.message).join(', ')
      }));
      setErrors(newErrors);
    } else {
      setErrors([]);
    }

    // Thêm file được chấp nhận
    if (acceptedFiles.length > 0) {
      const newFiles = acceptedFiles.map(file => ({
        file,
        id: Math.random().toString(36).substr(2, 9),
        preview: URL.createObjectURL(file)
      }));
      
      setSelectedFiles(prev => [...prev, ...newFiles]);
    }
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.gif', '.webp']
    },
    maxSize: 10 * 1024 * 1024, // 10MB
    multiple: true,
    disabled: disabled || isProcessing
  });

  const removeFile = (id) => {
    setSelectedFiles(prev => {
      const updated = prev.filter(item => item.id !== id);
      // Revoke URL để tránh memory leak
      const removed = prev.find(item => item.id === id);
      if (removed) {
        URL.revokeObjectURL(removed.preview);
      }
      return updated;
    });
  };

  const clearAll = () => {
    selectedFiles.forEach(item => URL.revokeObjectURL(item.preview));
    setSelectedFiles([]);
    setErrors([]);
  };

  const handleProcess = () => {
    if (selectedFiles.length > 0) {
      const files = selectedFiles.map(item => item.file);
      onFilesSelected(files);
    }
  };

  return (
    <div className="w-full max-w-4xl mx-auto p-6">
      {/* Dropzone */}
      <div
        {...getRootProps()}
        className={`
          border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors
          ${isDragActive
            ? 'border-blue-400 bg-blue-50'
            : 'border-gray-300 hover:border-gray-400'
          }
          ${(isProcessing || disabled) ? 'pointer-events-none opacity-50' : ''}
        `}
      >
        <input {...getInputProps()} />
        <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
        {isDragActive ? (
          <p className="text-lg text-blue-600">Thả hình ảnh vào đây...</p>
        ) : (
          <div>
            <p className="text-lg text-gray-600 mb-2">
              Kéo thả hình ảnh vào đây hoặc click để chọn file
            </p>
            <p className="text-sm text-gray-500 mb-1">
              Hỗ trợ: JPG, PNG, GIF, WebP (tối đa 10MB mỗi file)
            </p>
            <p className="text-xs text-blue-600 font-medium">
              💡 Khuyến nghị: Upload 5-8 ảnh/lần để tránh rate limit
            </p>
          </div>
        )}
      </div>

      {/* Hiển thị lỗi */}
      {errors.length > 0 && (
        <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-center mb-2">
            <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
            <h3 className="text-sm font-medium text-red-800">
              Một số file không thể tải lên:
            </h3>
          </div>
          <ul className="text-sm text-red-700">
            {errors.map((error, index) => (
              <li key={index}>
                <strong>{error.fileName}:</strong> {error.errors}
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* Hiển thị file đã chọn */}
      {selectedFiles.length > 0 && (
        <div className="mt-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium text-gray-900">
              Đã chọn {selectedFiles.length} hình ảnh
            </h3>
            <div className="space-x-2">
              <button
                onClick={clearAll}
                disabled={isProcessing}
                className="px-4 py-2 text-sm text-gray-600 hover:text-gray-800 disabled:opacity-50"
              >
                Xóa tất cả
              </button>
              <button
                onClick={handleProcess}
                disabled={isProcessing || selectedFiles.length === 0}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isProcessing ? 'Đang xử lý...' : 'Bắt đầu phân loại'}
              </button>
            </div>
          </div>

          {/* Grid hiển thị preview */}
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {selectedFiles.map((item) => (
              <div key={item.id} className="relative group">
                <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden">
                  <img
                    src={item.preview}
                    alt={item.file.name}
                    className="w-full h-full object-cover"
                  />
                </div>
                
                {/* Overlay với thông tin file */}
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-opacity rounded-lg flex items-center justify-center">
                  <button
                    onClick={() => removeFile(item.id)}
                    disabled={isProcessing}
                    className="opacity-0 group-hover:opacity-100 bg-red-500 text-white p-2 rounded-full hover:bg-red-600 transition-opacity disabled:opacity-50"
                  >
                    <X className="h-4 w-4" />
                  </button>
                </div>
                
                {/* Tên file */}
                <div className="mt-2 text-xs text-gray-600 truncate" title={item.file.name}>
                  {item.file.name}
                </div>
                
                {/* Kích thước file */}
                <div className="text-xs text-gray-500">
                  {(item.file.size / 1024 / 1024).toFixed(2)} MB
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default ImageUploader;
