# 🚀 Hướng dẫn Triển khai (Deployment Guide)

## 📋 Tổng quan

Ứng dụng Flower Classifier có thể được triển khai trên nhiều nền tảng khác nhau. Dưới đây là hướng dẫn chi tiết cho từng phương thức.

## 🔧 Chuẩn bị trước khi triển khai

### 1. Build ứng dụng
```bash
npm run build
```

### 2. Cấu hình Environment Variables
Tạo file `.env.production`:
```env
VITE_GEMINI_API_KEY=your_production_api_key
VITE_APP_NAME=Flower Classifier
VITE_APP_VERSION=1.0.0
```

### 3. Test build locally
```bash
npm run preview
```

## 🌐 Triển khai lên Vercel

### Bước 1: Cài đặt Vercel CLI
```bash
npm install -g vercel
```

### Bước 2: Login và deploy
```bash
vercel login
vercel
```

### Bước 3: Cấu hình Environment Variables
Trong Vercel Dashboard:
1. Vào Settings > Environment Variables
2. Thêm `VITE_GEMINI_API_KEY` với giá trị API key

### Bước 4: Cấu hình Domain (tùy chọn)
- Vào Settings > Domains
- Thêm custom domain nếu cần

## 📦 Triển khai lên Netlify

### Phương pháp 1: Drag & Drop
1. Build ứng dụng: `npm run build`
2. Vào [Netlify](https://netlify.com)
3. Kéo thả thư mục `dist/` vào Netlify

### Phương pháp 2: Git Integration
1. Push code lên GitHub/GitLab
2. Connect repository với Netlify
3. Cấu hình build settings:
   - Build command: `npm run build`
   - Publish directory: `dist`

### Phương pháp 3: Netlify CLI
```bash
npm install -g netlify-cli
netlify login
netlify deploy --prod --dir=dist
```

## ☁️ Triển khai lên Firebase Hosting

### Bước 1: Cài đặt Firebase CLI
```bash
npm install -g firebase-tools
```

### Bước 2: Login và init
```bash
firebase login
firebase init hosting
```

### Bước 3: Cấu hình firebase.json
```json
{
  "hosting": {
    "public": "dist",
    "ignore": [
      "firebase.json",
      "**/.*",
      "**/node_modules/**"
    ],
    "rewrites": [
      {
        "source": "**",
        "destination": "/index.html"
      }
    ]
  }
}
```

### Bước 4: Deploy
```bash
npm run build
firebase deploy
```

## 🐳 Triển khai với Docker

### Dockerfile
```dockerfile
# Build stage
FROM node:18-alpine as build
WORKDIR /app
COPY package*.json ./
RUN npm ci
COPY . .
RUN npm run build

# Production stage
FROM nginx:alpine
COPY --from=build /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

### nginx.conf
```nginx
events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    server {
        listen 80;
        server_name localhost;
        root /usr/share/nginx/html;
        index index.html;

        location / {
            try_files $uri $uri/ /index.html;
        }
    }
}
```

### Build và chạy Docker
```bash
docker build -t flower-classifier .
docker run -p 8080:80 flower-classifier
```

## 🔒 Bảo mật API Key

### 1. Environment Variables
Luôn sử dụng environment variables cho API keys:
```javascript
const API_KEY = import.meta.env.VITE_GEMINI_API_KEY;
```

### 2. Server-side Proxy (Khuyến nghị)
Tạo API proxy để ẩn API key:

```javascript
// api/classify.js (Vercel Functions)
export default async function handler(req, res) {
  const { image } = req.body;
  
  const response = await fetch('https://generativelanguage.googleapis.com/v1/models/gemini-1.5-flash:generateContent', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${process.env.GEMINI_API_KEY}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(image)
  });
  
  const data = await response.json();
  res.json(data);
}
```

### 3. Rate Limiting
Implement rate limiting để bảo vệ API:
```javascript
// middleware/rateLimit.js
const rateLimit = require('express-rate-limit');

const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100 // limit each IP to 100 requests per windowMs
});

module.exports = limiter;
```

## 📊 Monitoring và Analytics

### 1. Google Analytics
Thêm vào `index.html`:
```html
<!-- Google Analytics -->
<script async src="https://www.googletagmanager.com/gtag/js?id=GA_TRACKING_ID"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'GA_TRACKING_ID');
</script>
```

### 2. Error Tracking với Sentry
```bash
npm install @sentry/react
```

```javascript
// main.jsx
import * as Sentry from "@sentry/react";

Sentry.init({
  dsn: "YOUR_SENTRY_DSN",
  environment: import.meta.env.MODE,
});
```

## 🔧 Performance Optimization

### 1. Code Splitting
```javascript
// Lazy loading components
const ResultsDisplay = lazy(() => import('./components/ResultsDisplay'));
```

### 2. Image Optimization
```javascript
// Compress images before processing
const compressImage = (file, quality = 0.8) => {
  return new Promise((resolve) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();
    
    img.onload = () => {
      canvas.width = img.width;
      canvas.height = img.height;
      ctx.drawImage(img, 0, 0);
      
      canvas.toBlob(resolve, 'image/jpeg', quality);
    };
    
    img.src = URL.createObjectURL(file);
  });
};
```

### 3. Caching Strategy
```javascript
// Service Worker for caching
const CACHE_NAME = 'flower-classifier-v1';
const urlsToCache = [
  '/',
  '/static/js/bundle.js',
  '/static/css/main.css'
];

self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => cache.addAll(urlsToCache))
  );
});
```

## 🚨 Troubleshooting

### Lỗi thường gặp:

1. **Build fails**: Kiểm tra dependencies và environment variables
2. **API key không hoạt động**: Verify API key và permissions
3. **CORS errors**: Cấu hình proxy hoặc server-side API
4. **Performance issues**: Implement lazy loading và image compression

### Debug commands:
```bash
# Check build output
npm run build -- --debug

# Analyze bundle size
npm install -g webpack-bundle-analyzer
npx webpack-bundle-analyzer dist/static/js/*.js
```

## 📞 Hỗ trợ

Nếu gặp vấn đề trong quá trình triển khai:
1. Kiểm tra logs của platform hosting
2. Verify environment variables
3. Test locally trước khi deploy
4. Liên hệ support team qua GitHub Issues
